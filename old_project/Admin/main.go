package main

import (
	"Admin/config"
	"Admin/dal"
	"Admin/internal/gofiber"
	"Admin/internal/logger"
	"Admin/internal/task"
	"Admin/internal/transfer"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

func main() {
	fx.New(
		fx.Provide(
			logger.NewLogger,
			config.NewConfig,
			dal.NewGormDB,
			gofiber.NewFiberApp,
		),
		fx.Invoke(
			func(*gorm.DB) {},
			transfer.InitEthNet,
			transfer.InitTron,
			//transfer.InitBscNet,
			transfer.Pool,
			task.Pool,
			gofiber.RunFiberServer,
		),
	).Run()
}
