
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>商户后台主页</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href='/layuiadmin/layui/css/layui.css' media="all">
  <link rel="stylesheet" href='/layuiadmin/style/admin.css' media="all">
</head>
<body>



<div class="layui-fluid">
  <div class="layui-row layui-col-space15">   
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
        今日跑量
          <span class="layui-badge layui-bg-blue layuiadmin-badge">跑量</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="todaymoney">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
        7日跑量
          <span class="layui-badge layui-bg-blue layuiadmin-badge">跑量</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="weekmoney">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          本月跑量
          <span class="layui-badge layui-bg-blue layuiadmin-badge">跑量</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="monthmoney">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          上月跑量
          <span class="layui-badge layui-bg-blue layuiadmin-badge">跑量</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="lastmonthmoney">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
        今日盈利
          <span class="layui-badge layui-bg-green layuiadmin-badge">盈利</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="todayfee">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
        7日盈利
          <span class="layui-badge layui-bg-green layuiadmin-badge">盈利</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="weekfee">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          本月盈利
          <span class="layui-badge layui-bg-green layuiadmin-badge">盈利</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="monthfee">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          上月盈利
          <span class="layui-badge layui-bg-green layuiadmin-badge">盈利</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="lastmonthfee">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          今日收款订单数
          <span class="layui-badge layui-bg-black layuiadmin-badge">收款</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="todayc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          7日收款订单数
          <span class="layui-badge layui-bg-black layuiadmin-badge">收款</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="weekc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          本月收款订单数
          <span class="layui-badge layui-bg-black layuiadmin-badge">收款</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="monthc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          上月收款订单数
          <span class="layui-badge layui-bg-black layuiadmin-badge">收款</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="lastmonthc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          今日下发订单数
          <span class="layui-badge layui-bg-orange layuiadmin-badge">下发</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="todaypc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          7日下发订单数
          <span class="layui-badge layui-bg-orange layuiadmin-badge">下发</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="weekpc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          本月下发订单数
          <span class="layui-badge layui-bg-orange layuiadmin-badge">下发</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="monthpc">0</p>
        </div>
      </div>
    </div>
    <div class="layui-col-sm6 layui-col-md3">
      <div class="layui-card">
        <div class="layui-card-header">
          上月下发订单数
          <span class="layui-badge layui-bg-orange layuiadmin-badge">下发</span>
        </div>
        <div class="layui-card-body layuiadmin-card-list">
          <p class="layuiadmin-big-font" id="lastmonthpc">0</p>
        </div>
      </div>
    </div>
    <!--<div class="layui-col-sm12">-->
    <!--  <div class="layui-card">-->
    <!--    <a lay-href="order/orderList.html" lay-tips="前往订单管理" lay-text="订单管理"><div class="layui-card-header">最近订单</div></a>-->
    <!--    <div class="layui-card-body">-->
    <!--          <table class="layui-table layuiadmin-page-table" lay-skin="line" id="LAY-user-manage" lay-filter="LAY-user-manage">-->
    <!--          </table>-->
    <!--        </div>-->
    <!--  </div>-->
    <!--</div>-->
        
    </div>
  </div>
</div>

<script src="/layuiadmin/layui/layui.js"></script>
<script src="/JS/public.js"></script>

<script type="text/javascript">
  layui.use(['jquery'], function(args){
      var $= layui.jquery;


      window.onload = function(){
                // $.get(interfaceUrl + '/Mapi/GetConsole?id='+admin_info.id,function(res_data){
                //             $('#today_order_count').html(res_data.data.today_order_count);
                //             $('#today_success_order_count').html(res_data.data.today_success_order_count);
                //             $('#week_order_count').html(res_data.data.week_order_count);
                //             $('#week_success_order_count').html(res_data.data.week_success_order_count);
                //             $('#month_order_count').html(res_data.data.month_order_count);
                //             $('#month_success_order_count').html(res_data.data.month_success_order_count);
                // });
        var formData = new FormData();
        $.ajax({
          type: 'POST',
          url: interfaceUrl + '/Stat/GetConsole',
          beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
          },
          data: formData, // 使用 FormData 对象
          processData: false,
          contentType: false,
          success: function (res_data) {
            if(res_data.code == 0){
              console.log(res_data);
              $('#todaymoney').html(res_data.data.DaySuccessMoney);
              $('#weekmoney').html(res_data.data.WeekSuccessMoney);
              $('#monthmoney').html(res_data.data.MonthSuccessMoney);
              $('#lastmonthmoney').html(res_data.data.LastMonthSuccessMoney);
              $('#todayfee').html(res_data.data.DayFeeMoney);
              $('#weekfee').html(res_data.data.WeekFeeMoney);
              $('#monthfee').html(res_data.data.MonthFeeMoney);
              $('#lastmonthfee').html(res_data.data.LastMonthFeeMoney);
              $('#todayc').html(res_data.data.DaySuccessC);
              $('#weekc').html(res_data.data.WeekSuccessC);
              $('#monthc').html(res_data.data.MonthSuccessC);
              $('#lastmonthc').html(res_data.data.LastMonthSuccessC);
              $('#todaypc').html(res_data.data.DaySuccessPC);
              $('#weekpc').html(res_data.data.WeekSuccessPC);
              $('#monthpc').html(res_data.data.MonthSuccessPC);
              $('#lastmonthpc').html(res_data.data.LastMonthSuccessPC);
            }else{
              if (res_data.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
              }
            }
          },
        });
  }
  });
  
</script>
</body>
</html>

