layui.config({
    base: '/layuiadmin/' // 静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'form'], function(){
    var $ = layui.$
        ,admin = layui.admin
        ,element = layui.element
        ,form = layui.form;

    // form.render(null, 'component-form-element');
    // element.render('breadcrumb', 'breadcrumb');

    form.on('submit(component-form-element)', function(data){
        var formData = new FormData();
        formData.append('trc1', $('#trc1').val());
        formData.append('trc2', $('#trc2').val());
        formData.append('col', $('#col').val());
        $.ajax({
            type: 'POST',
            url: interfaceUrl + '/Config/UpdateConfig',
            beforeSend: function (xhr) {
                xhr.setRequestHeader("Authorization", token);
            },
            data: formData, // 使用 FormData 对象
            processData: false,
            contentType: false,
            success: function (data) {
                if (data.code == true) {
                    layer.msg('修改成功');
                    setTimeout(function() {
                        location.reload(); // 刷新当前页面
                    }, 1000); // 1000 毫秒后执行
                } else {
                    layer.msg(data.msg);
                }
            },
        });
    });

    $.ajax({
        type: 'POST',
        url: interfaceUrl + '/Config/GetConfigInfo',
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
        },
        success: function (data) {
            if (data.code == true) {
                $('#trc1').val(data.data.trcfee1);
                $('#trc2').val(data.data.trcfee2);
                $('#col').val(data.data.col);
            } else {
                layer.msg(data.msg);
            }
        },
    });
});