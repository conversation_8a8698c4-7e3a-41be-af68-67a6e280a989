layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['upload', 'element', 'layer'], function(){
    var $ = layui.$
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer
        ,form = layui.form
        , laydate = layui.laydate;


    // 获取角色列表
    var formData = new FormData();
    formData.append('limit', 10000);
    formData.append('offset', 0);
    $.ajax({
        type: 'POST',
        url: interfaceUrl + '/Role/GetRoleList',
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
        },
        data: formData, // 使用 FormData 对象
        processData: false,
        contentType: false,
        success: function (res_data) {
            if(res_data.code == 0){
                var str = '';
                str += '<option value="0">无</option>';
                for($i=0;$i<res_data.data.length;$i++){
                    str += '<option value="'+res_data.data[$i].id+'">'+res_data.data[$i].name+'</option>';
                }
                $("#role").append(str);
            }else{
                if (res_data.msg == 'Token无效'){
                    window.location.href='/login.html';
                    return false;
                }
            }
            form.render();
        },
    });
})
