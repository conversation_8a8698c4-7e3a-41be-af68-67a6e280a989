layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }

        , add: function () {
            layer.open({
                type: 2
                , title: '添加管理员'
                , content: 'createAdmin.html'
                , area: ['600px', '600px']
                , btn: ['确定', '取消']
                , yes: function (index, layero) {
                    var iframeWindow = window['layui-layer-iframe' + index]
                        , submit = layero.find('iframe').contents().find("#LAY-createAdmin-submit");

                    //监听提交
                    iframeWindow.layui.form.on('submit(LAY-createAdmin-submit)', function (data) {
                        var field = data.field; //获取提交的字段
                        $.ajax({
                            url: interfaceUrl + '/Admin/InsertAdmin',
                            headers: { "Authorization": token },
                            type: 'POST',
                            data: field,
                            success: function (res_data) {                                
                                layer.msg(
                                    res_data.msg,
                                    {time : 3000},
                                    function() {
                                        location.reload();
                                    }
                                );
                            },
                        });
                        layer.close(index); //关闭弹层
                    });

                    submit.trigger('click');
                }
            });
        }
    };

    

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Admin/GetAdminList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'username', title: '用户名'}
            , {field: 'name', title: '角色'}
            , {field: 'status', title: '状态',
                templet: function (provider) {
                    if(provider.status == 0){
                        return '正常';
                    }else if(provider.status == 1){
                        return '禁用';
                    }
                }
            }
            , {field: 'ctime', title: '创建时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
            , {field: 'add', title: '操作', width: 200, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        if (obj.event == 'enban') {
            layer.confirm('确定启用此管理员？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 0);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Admin/UpdateAdmin',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
        if (obj.event == 'ban') {
            layer.confirm('确定禁用此管理员？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 1);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Admin/UpdateAdmin',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }

        if (obj.event == 'pwd') {
            layer.prompt({
                title: '请输入新的密码',
                formType: 1  //密码框
            }, function (value, index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('password', value);  // 添加密码字段
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Admin/UpdatePassword',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
                layer.close(index);  // 关闭输入框
            });
        }
    });

    var endDate= laydate.render({
		elem: '#end_time',//选择器结束时间
		type: 'datetime',
		min:"1970-1-1",//设置min默认最小值
		done: function(value,date){
		      $('#end_time').change(); 
			startDate.config.max={
				year:date.year,
				month:date.month-1,//关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			}
		}
	});
	//日期范围
	var startDate=laydate.render({
		elem: '#start_time',
		type: 'datetime',
		max:"2099-12-31",//设置一个默认最大值
		done: function(value, date){
		    $('#start_time').change(); 
			endDate.config.min ={
				year:date.year,
				month:date.month-1, //关键
				date: date.date,
				hours: 0,
				minutes: 0,
				seconds : 0
			};
		}
	});

});