layui.config({
    base: 'layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use('index');

function logout(){
    var params = {};
    $.ajax({
        type: 'POST', // 改为 POST 请求
        url: interfaceUrl + '/Auth/Logout',
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
        },
        data: params, // 发送 params 对象
        success: function (data) {
            if (data.code == true) {
                localStorage.clear();
                layer.msg('退出成功！')
                window.location.href = '/login.html';
            } else {
                layer.msg(data.msg);
                layer.close(loginLoadIndex);
            }
        },
    });
}