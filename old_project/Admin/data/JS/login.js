if(window.top !== window.self){ //若自身窗口不等于顶层窗口
    window.top.location.href = window.self.location.href; //顶层窗口跳转到自身窗口
}
layui.use(['layer'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table

    $(function () {
        // 登陆事件
        $('#loginBtn').click(function () {
            login();
        });
        // 注册事件
        $('#loginRegister').click(function () {
            register();
        });
    });


// 登录流程
    function login() {
        var loginUsername = $('#LAY-user-login-username').val();
        var loginPassword = $('#LAY-user-login-password').val();
        var params = {};
        params.username = loginUsername;
        params.password = loginPassword;
        var loginLoadIndex = layer.load(2);
        var date = new Date();
        $.ajax({
            type: 'POST',
            url: interfaceUrl + '/Auth/Login',
            dataType: 'json',
            data: params, // 直接发送 params 对象
            success: function (data) {
                if (data.code == true) {
                    localStorage.setItem("Authorization", data.data.token);
                    localStorage.setItem("time_src", date.getTime());
                    localStorage.setItem("admin_info", JSON.stringify(data.data));
                    layer.msg('登录成功！')
                    window.location.href = '/index.html';
                } else {
                    layer.msg(data.msg);
                    layer.close(loginLoadIndex);
                }
            },
        });
    }
})