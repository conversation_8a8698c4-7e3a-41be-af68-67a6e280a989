// 接口地址
var interfaceUrl = 'http://127.0.0.1:8888';
//var interfaceUrl = '/';
//获取url的值
function getQueryString(name) {
    var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}
 token = localStorage.getItem("Authorization")
 //时间戳的处理

 layui.laytpl.toDateString = function(d, format){

    var date = new Date(d || new Date())

    ,ymd = [

    this.digit(date.getFullYear(), 4)

    ,this.digit(date.getMonth() + 1)

    ,this.digit(date.getDate())

    ]

    ,hms = [

    this.digit(date.getHours())

    ,this.digit(date.getMinutes())

    ,this.digit(date.getSeconds())

    ];




    format = format || 'yyyy-MM-dd HH:mm:ss';




    return format.replace(/yyyy/g, ymd[0])

    .replace(/MM/g, ymd[1])

    .replace(/dd/g, ymd[2])

    .replace(/HH/g, hms[0])

    .replace(/mm/g, hms[1])

    .replace(/ss/g, hms[2]);

   };
  
    
  
   //数字前置补零
  
   layui.laytpl.digit = function(num, length, end){
  
    var str = '';
  
    num = String(num);
  
    length = length || 2;
  
    for(var i = num.length; i < length; i++){
  
    str += '0';
  
    }
  
    return num < Math.pow(10, length) ? str + (num|0) : num;
  
   };


