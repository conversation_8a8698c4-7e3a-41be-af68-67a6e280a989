layui.config({
    base: '/layuiadmin/' // 静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'form'], function(){
    var $ = layui.$
        ,admin = layui.admin
        ,element = layui.element
        ,form = layui.form;

    var button = document.getElementById('back');

    button.addEventListener('click', function(event) {
        event.preventDefault();
        window.location.href='/order/orderList.html';
    });

    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }
    var id = getQueryString("id");
    var formData = new FormData();
    formData.append('id', id);
    $.ajax({
        type: 'POST',
        url: interfaceUrl + '/Order/GetOrderInfo',
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
        },
        data: formData, // 使用 FormData 对象
        processData: false,
        contentType: false,
        success: function (res_data) {
            if(res_data.code == true){
                $('#orderid').val(res_data.data.orderid);
                $('#rorderid').val(res_data.data.rorderid);
                $('#mchname').val(res_data.data.merchant_name);
                $('#addr').val(res_data.data.trxadd);
                $('#transactionid').val(res_data.data.transactionid);
                $('#money').val(res_data.data.money+' USDT');
                $('#realmoney').val(res_data.data.realmoney+' USDT');
                if(res_data.data.status == 0){
                    if(res_data.data.realmoney != null && res_data.data.transactionid != null){
                        $('#status').val('入账待处理');
                    }else{
                        $('#status').val('创建订单');
                    }
                }else if(res_data.data.status == 1){
                    $('#status').val('订单超时');
                }else if(res_data.data.status == 2){
                    $('#status').val('支付成功');
                }else if(res_data.data.status == 3){
                    $('#status').val('支付成功');
                }else if(res_data.data.status == 4){
                    $('#status').val('手动成功');
                }else if(res_data.data.status == 5){
                    $('#status').val('已取消');
                }
                if(res_data.data.status == 3){
                    $('#callbackstatus').val('回调成功');
                }else if(res_data.data.status == 2 && res_data.data.callbacknum >= 5){
                    $('#callbackstatus').val('回调失败');
                }else if(res_data.data.status == 2 && res_data.data.callbacknum < 5){
                    $('#callbackstatus').val('回调中');
                }else{
                    $('#callbackstatus').val('未回调');
                }
                $('#callbackurl').val(res_data.data.callbackurl);
                $('#callbacknum').val(res_data.data.callbacknum);
                $('#ctime').val(layui.laytpl.toDateString(res_data.data.ctime*1000));
                $('#etime').val(layui.laytpl.toDateString(res_data.data.etime*1000));
                form.render('select');
            }else{
                if (res_data.msg == 'Token无效'){
                    window.location.href='/login.html';
                    return false;
                }
            }
        },
    });


});