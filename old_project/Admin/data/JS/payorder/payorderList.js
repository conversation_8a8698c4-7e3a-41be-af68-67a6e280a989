layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/PayOrder/GetPayOrderList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'orderid',  title: '系统订单号'}
            , {field: 'rorderid', title: '商户订单号'}
            , {field: 'merchant_name', title: '商户名', width: 100}
            // , {field: 'coin', title: '币种', width: 100}
            , {field: 'money', title: '下发金额', width: 100}
            // , {field: 'tran_fee', title: '预计矿工费'}
            // , {field: 'real_tran_fee', title: '实际矿工费'}
            , {field: 'addr', title: '下发地址', width: 330}
            , {field: 'status', title: '状态',
                templet: function (provider) {
                    if(provider.status == 0){
                        return '创建订单';
                    }else if(provider.status == 1){
                        return '订单超时';
                    }else if(provider.status == 2){
                        return '转账成功';
                    }else if(provider.status == 3){
                        return '转账成功';
                    }else if(provider.status == 4){
                        return '手动成功';
                    }else if(provider.status == 5){
                        return '转账失败';
                    }
                }
                , width: 100}
            , {field: 'status', title: '回调状态',
                templet: function (provider) {
                    if(provider.status == 3){
                        return '回调成功';
                    }else if(provider.status == 2 && provider.callbacknum >= 5){
                        return '回调失败';
                    }else if(provider.status == 2 && provider.callbacknum < 5){
                        return '回调中';
                    }else{
                        return '未回调';
                    }
                }
                , width: 100}
            , {field: 'callbacknum', title: '回调次数', width: 100}
            , {field: 'ctime', title: '下单时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
            // , {field: 'etime', title: '完成时间', templet: '<div>{{ layui.laytpl.toDateString(d.etime*1000) }}</div>'}
            , {field: 'add', title: '操作', width: 250, toolbar: "#table-useradmin-webuser"}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据
        if (obj.event == 'reissue') {
            layer.confirm('确定补发回调吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/PayOrder/DoCallbackPayOrder',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
        
        if (obj.event == 'web') {
            if (obj.data.chain == 'Trc20'){
                window.open('https://tronscan.org/#/transaction/'+obj.data.transactionid)
            }else if (obj.data.chain == 'Erc20'){
                window.open('https://etherscan.io/tx/'+obj.data.transactionid)
            }else{
                window.open('https://bscscan.com/tx/'+obj.data.transactionid)
            }

        }

        if(obj.event === 'detail'){ // 如果点击的是"详情"按钮
            window.location.href = '/payorder/detail.html?id=' + obj.data.id;
        }
    });

});
