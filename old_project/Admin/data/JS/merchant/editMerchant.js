layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['upload', 'element', 'layer','form'], function(){
    var $ = layui.$
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer
        ,form = layui.form
        , laydate = layui.laydate;


    function getQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }
    var id = getQueryString("id");
    var formData = new FormData();
    formData.append('id', id);
    $.ajax({
        type: 'POST',
        url: interfaceUrl + '/Merchant/GetMerchantInfo',
        beforeSend: function (xhr) {
            xhr.setRequestHeader("Authorization", token);
        },
        data: formData, // 使用 FormData 对象
        processData: false,
        contentType: false,
        success: function (res_data) {
            if(res_data.code == true){
                $('#trcfee').val(res_data.data.fee);
                $('#ethfee').val(res_data.data.ethfee);
                $('#bscfee').val(res_data.data.bscfee);
                $('#credits').val(res_data.data.credits);
                $('#is_floatdown').val(res_data.data.is_floatdown);
                $('#gather').val(res_data.data.gather_type);
                $('#mchid').val(res_data.data.id);
                form.render('select');
            }else{
                if (res_data.msg == 'Token无效'){
                    window.location.href='/login.html';
                    return false;
                }
            }
        },
    });
})