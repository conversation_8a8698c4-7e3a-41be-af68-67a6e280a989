layui.config({
    base: '/layuiadmin/' //静态资源所在路径
}).extend({
    index: 'lib/index' //主入口模块
}).use(['index', 'table', 'laydate'], function () {
    var $ = layui.$
        , form = layui.form
        , table = layui.table
        , laydate = layui.laydate;

    //监听搜索
    form.on('submit(LAY-user-front-search)', function (data) {
        var field = data.field;
        //执行重载
        table.reload('LAY-user-manage', {
            where: field
        });
    });

    //事件
    var active = {
        batchdel: function () {
            var checkStatus = table.checkStatus('LAY-user-manage')
                , checkData = checkStatus.data; //得到选中的数据
            alert(111);
            if (checkData.length === 0) {
                return layer.msg('请选择数据');
            }

            layer.prompt({
                formType: 1
                , title: '敏感操作，请验证口令'
            }, function (value, index) {
                layer.close(index);


            });
        }
    };

    $('.layui-btn.layuiadmin-btn-useradmin').on('click', function () {
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });

    //定义返回格式
    var parseDataFun = function (res) {
        return {
            "code": res.code == 200,
            "message": res.msg,
            "data": res.data.data,
        };
    };
    //渲染表格
    table.render({
        elem: '#LAY-user-manage'
        , url: interfaceUrl + '/Settlement/GetSettlementList'
        , headers: { "Authorization": token}
        , parseData: parseDataFun.data
        , method: 'post'
        , xhrFields: {withCredentials: true}
        // , cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
        , cols: [[
            {field: 'username',  title: '商户名'}
            , {field: 'address', title: '收款地址'}
            , {field: 'money', title: '归集金额'}
            , {field: 'needmoney', title: '归集后地址余额'}
            , {field: 'status', title: '状态',
                templet: function (provider) {
                    if(provider.status == 0){
                        return '待处理';
                    }else if(provider.status == 1){
                        return '转账USDT中';
                    }else if(provider.status == 2){
                        return '已完成';
                    }else if(provider.status == 2){
                        return '转账手续费中';
                    }
                }
            }
            , {field: 'ctime', title: '申请时间', templet: '<div>{{ layui.laytpl.toDateString(d.ctime*1000) }}</div>'}
        ]]
        , page: true
        , limit: 30
        , limits: [30, 50, 100]
        , done: function (res) {
            if (res.msg == 'Token无效'){
                window.location.href='/login.html';
                return false;
            }
        }
        // ,layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
        , jump: function (obj, first) {
            if (!first) {
                layer.msg('第 ' + obj.curr + ' 页');
            }
        }
    });
    //检测行级数据
    table.on('tool(LAY-user-manage)', function (obj) { //注：tool是工具条事件名，test是table原始容器的属性 lay-filter="对应的值"
        //删除数据
        if (obj.event == 'del') {
            layer.confirm('确定取消这笔结算申请吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 2);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Settlement/UpdateSettlement',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }else if (obj.event == 'do') {
            layer.confirm('确定已结算这笔结算申请吗？', function (index) {
                var formData = new FormData();
                formData.append('id', obj.data.id);
                formData.append('status', 1);
                $.ajax({
                    type: 'POST',
                    url: interfaceUrl + '/Settlement/UpdateSettlement',
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader("Authorization", token);
                    },
                    data: formData, // 使用 FormData 对象
                    processData: false,
                    contentType: false,
                    success: function (res_data) {
                        if(res_data.code == true){
                            layer.msg(res_data.msg);
                            table.reload('LAY-user-manage');
                        }else{
                            layer.msg(res_data.msg);
                        }
                    },
                });
            });
        }
    });

    var endDate= laydate.render({
        elem: '#end_time',//选择器结束时间
        type: 'datetime',
        min:"1970-1-1",//设置min默认最小值
        done: function(value,date){
            $('#end_time').change();
            startDate.config.max={
                year:date.year,
                month:date.month-1,//关键
                date: date.date,
                hours: 0,
                minutes: 0,
                seconds : 0
            }
        }
    });
    //日期范围
    var startDate=laydate.render({
        elem: '#start_time',
        type: 'datetime',
        max:"2099-12-31",//设置一个默认最大值
        done: function(value, date){
            $('#start_time').change();
            endDate.config.min ={
                year:date.year,
                month:date.month-1, //关键
                date: date.date,
                hours: 0,
                minutes: 0,
                seconds : 0
            };
        }
    });

});