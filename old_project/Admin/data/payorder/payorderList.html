<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-form layui-card-header layuiadmin-card-header-auto">
            <div class="layui-form-item">
              <div class="layui-inline">
                <label class="layui-form-label">商户订单号</label>
                <div class="layui-input-block">
                  <input type="text" name="rorderid" id="rorderid" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-inline">
                <label class="layui-form-label">系统订单号</label>
                <div class="layui-input-block">
                  <input type="text" name="orderid" id="orderid" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
              </div>
              <!--<div class="layui-inline">-->
              <!--      <label class="layui-form-label">Hash</label>-->
              <!--      <div class="layui-input-block">-->
              <!--        <input type="text" name="hash" id="hash" placeholder="请输入" autocomplete="off" class="layui-input">-->
              <!--      </div>-->
              <!--    </div>-->
                <div class="layui-inline">
                    <label class="layui-form-label">下发地址</label>
                    <div class="layui-input-block">
                      <input type="text" name="addr" id="addr" placeholder="请输入" autocomplete="off" class="layui-input">
                    </div>
                  </div>
                  <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                      <select name="status" id="status">
                        <option value="">不限</option>
                        <option value="0">创建订单</option>
                        <option value="1">订单超时</option>
                        <option value="2">转账成功</option>
                        <option value="5">转账失败</option>
                      </select>
                    </div>
                  </div>
<!--                <div class="layui-inline">-->
<!--                    <label class="layui-form-label">链</label>-->
<!--                    <div class="layui-input-block">-->
<!--                        <select name="chain" id="chain">-->
<!--                            <option value="">不限</option>-->
<!--                            <option value="Trc20">TRX</option>-->
<!--                            <option value="Erc20">ETH</option>-->
<!--                        </select>-->
<!--                    </div>-->
<!--                </div>-->
                <div class="layui-inline">
                    <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="LAY-user-front-search">
                      <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                    </button>
                  </div>
            </div>
        </div>

        <div class="layui-card-body">

            <table id="LAY-user-manage" lay-filter="LAY-user-manage"></table>
            <script type="text/html" id="table-useradmin-webuser">
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="detail">查看详情</a>
                {{#  if(d.status == 0 ){ }}

                {{#  } else if (d.status == 2 || d.status == 3){ }}
              <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="reissue">补发回调</a>
                <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="web">区块浏览器</a>
                {{#  } }}
          </script>
        </div>
    </div>
</div>
<script src='/JS/jquery-3.5.1.min.js'></script>
<script src="/layuiadmin/layui/layui.js"></script>
<script src="/JS/public.js"></script>

<script src="/JS/payorder/payorderList.js"></script>
</body>
</html>
