

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>消息中心</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/layuiadmin/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/layuiadmin/style/admin.css" media="all">
  <script src="/JS/public.js"></script>

  <script src="/layuiadmin/layui/layui.js"></script>
</head>
<body>

  <div class="layui-fluid" id="LAY-app-message">
    <div class="layui-card">
      <div class="layui-tab layui-tab-brief">

        <div class="layui-tab-content">
        
          <div class="layui-tab-item layui-show">
            <div class="layui-table-box">
   
   <div class="layui-table-body layui-table-main" style="height:480px;">
    <table cellspacing="0" cellpadding="0" border="0" class="layui-table" style="width:50%;margin:0 auto;margin-top:100px;">
     <tbody>

       

      <tr data-index="0" class="">
        <td data-field="id" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-id">
          <p style="float:right;margin-right:150px;">商户ID:</p>
         </div></td>
        <td data-field="username" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
          <p id="appid"></p>
         </div></td>
 
        
 
       </tr>
      <tr data-index="0" class="">
        <td data-field="id" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-id">
          <p style="float:right;margin-right:150px;">ApiKey:</p>
         </div></td>
        <td data-field="username" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
          <span id="appkey"></span>&nbsp;&nbsp;<a id="reappkey" style="cursor:pointer;">点击刷新</a>
         </div></td>
 
        
 
       </tr>
       <tr data-index="0" class="">
        <td data-field="id" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-id">
          <p style="float:right;margin-right:150px;">可用余额:</p>
         </div></td>
        <td data-field="username" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
          <p id="money"></p>
         </div></td>
        </tr>
        <tr data-index="0" class="">
          <td data-field="id" style="width:50%;">
           <div class="layui-table-cell laytable-cell-4-id">
            <p style="float:right;margin-right:150px;">冻结余额:</p>
           </div></td>
          <td data-field="username" style="width:50%;">
           <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
            <p id="lock_money"></p>
           </div></td>
          </tr>
          <tr data-index="0" class="">
            <td data-field="id" style="width:50%;">
             <div class="layui-table-cell laytable-cell-4-id">
              <p style="float:right;margin-right:150px;">手续费余额:</p>
             </div></td>
            <td data-field="username" style="width:50%;">
             <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
              <p id="fee_money"></p>
             </div></td>
            </tr>
 
        

      <tr data-index="0" class="">
       <td data-field="id" style="width:50%;">
        <div class="layui-table-cell laytable-cell-4-id">
         <p style="float:right;margin-right:150px;">谷歌身份验证:</p>
        </div></td>
       <td data-field="username" style="width:50%;">
        <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
            <a style="cursor:pointer;" id="open">点击开启</a>
        </div></td>

       

      </tr>
      <tr data-index="0" class="">
        <td data-field="id" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-id">
          <p style="float:right;margin-right:150px;">修改密码:</p>
         </div></td>
        <td data-field="username" style="width:50%;">
         <div class="layui-table-cell laytable-cell-4-username" style="text-align:center">
             <a href="/password.html" style="cursor:pointer;">点击修改</a>
         </div></td>
 
        
 
       </tr>
       
      
      
     </tbody>
    </table>
    
   </div>
   <div style="text-align:center;margin-bottom:20px;"><p style="font-size:20px;" id="miyao"></p></div>
   <div style="text-align:center"><img id="qrCode" src="" alt=""></div>
   <div style="text-align:center;margin-top:20px;display:none;" id="anniu"><button style="display: inline-block;
                                                padding: 6px 12px;
                                                margin-bottom: 0;
                                                font-size: 20px;
                                                font-weight: 400;
                                                line-height: 1.42857143;
                                                text-align: center;
                                                white-space: nowrap;
                                                vertical-align: middle;
                                                -ms-touch-action: manipulation;
                                                touch-action: manipulation;
                                                cursor: pointer;
                                                -webkit-user-select: none;
                                                -moz-user-select: none;
                                                -ms-user-select: none;
                                                user-select: none;
                                                background-image: none;
                                                border: 1px solid transparent;
                                                border-radius: 4px;
                                                color: #fff;
                                                background-color: #337ab7;
                                                border-color: #2e6da4;">下一步</button></div>
   
  </div>
            
            
          </div>
          
        </div>
      </div>
    </div>
  </div>

  <script src="/layuiadmin/layui/layui.js"></script>
  <script>
  layui.use(['jquery'], function(args){
      var $= layui.jquery;
      window.onload = function(){
        $.ajax({  
            headers: {  
                Accept: "application/json; charset=utf-8" ,
                Authorization: token,
                ID: admin_info.id
            },  
            type: "get",  
            url: interfaceUrl + '/Mapi/CheckToken',
            success: function (data) { 
                // console.log(data); 
                if (!data.code){
                    localStorage.clear();
                    layer.msg('登录超时，请重新登录！');
                    window.location.href = '/login.html';
                }
            },error:function(error){
                console.log(error);
            }

        });
        $.ajax({  
                  headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                  },  
					  type: "get",  
					  url: interfaceUrl + '/Mapi/GetUser?id='+admin_info.id,
					  success: function (res_data) { 
						  // console.log(res_data); 
						  if (!res_data.code){
							  layer.msg('服务器发生异常!');
						  }else{
                            $('#appkey').html(res_data.data.appkey);
                            $('#appid').html(res_data.data.appid);
                            $('#rate').html(res_data.data.rate+'CNY');
                            $('#money').html(res_data.data.money);
                            $('#lock_money').html(res_data.data.lock_money);
                            $('#fee_money').html(res_data.data.fee_money);
						  }
					  },error:function(error){
						  console.log(error);
					  }

			  });
                // $.get(interfaceUrl + '/Mapi/GetUser?id='+admin_info.id,function(res_data){
                //             $('#appkey').html(res_data.data.appkey);
                //             $('#money').html(res_data.data.money);
                // });
      }

      function isNumber(value) {         //验证是否为数字
          var patrn = /^(-)?\d+(\.\d+)?$/;
          if (patrn.exec(value) == null || value == "") {
              return false
          } else {
              return true
          }
      }

      $('#rerate').click(function(){
        var tess = prompt("请输入新汇率");
        if(!isNumber(tess)){
          alert('汇率只能为数字');
          return false;
        }
        $.ajax({  
                  headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                  },  
					  type: "get",  
					  url: interfaceUrl + '/Mapi/ReRate?id='+admin_info.id+'&rate='+tess,
					  success: function (data) { 
						  // console.log(data); 
						  if (!data.code){
							  layer.msg('服务器发生异常!');
						  }else{
                $('#rate').html(data.data+'CNY');
						  }
					  },error:function(error){
						  console.log(error);
					  }

			  });
    });

      $('#reappkey').click(function(){
        if(confirm('确定要重新生成AppKey吗？')){
          $.ajax({  
                  headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                  },  
					  type: "get",  
					  url: interfaceUrl + '/Mapi/RefreshAppKey?id='+admin_info.id,
					  success: function (data) { 
						  // console.log(data); 
						  if (!data.code){
							  layer.msg('服务器发生异常!');
						  }else{
                $('#appkey').html(data.data);
						  }
					  },error:function(error){
						  console.log(error);
					  }

			  });
        
      }
    });

      $('#open').click(function(){
        $.ajax({  
                  headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                  },  
					  type: "get",  
					  url: interfaceUrl + '/Mapi/OpenGoogle?id='+admin_info.id,
					  success: function (data) { 
						  // console.log(data); 
						  if (!data.code){
							  layer.msg('服务器发生异常!');
						  }else{
                            $('#miyao').html('密钥:'+data.secret);
                            $('#miyao').attr('data',data.secret);
                            $('#qrCode').attr('src',data.qrCode);
                            $('#anniu').css('display','block');
                            alert('请在您的身份验证器中使用下方密钥或扫描二维码，设置完成后请点击下一步按钮');
						  }
					  },error:function(error){
						  console.log(error);
					  }

			  });
        // $.get(interfaceUrl + '/Mapi/OpenGoogle?id='+admin_info.id,function(data){
        //                                 $('#miyao').html('密钥:'+data.secret);
        //                                 $('#miyao').attr('data',data.secret);
        //                                 $('#qrCode').attr('src',data.qrCode);
        //                                 $('#anniu').css('display','block');
        //                                 alert('请在您的身份验证器中使用下方密钥或扫描二维码，设置完成后请点击下一步按钮');
        //                         });
      })

      $('#anniu').click(function(){
        var secret = $('#miyao').attr('data');
        var code;
        code=prompt("请输入谷歌身份验证码"); 
        $.ajax({  
                  headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                  },  
					  type: "get",  
					  url: interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id+"&code="+code+"&secret="+secret,//{'code':code,'secret':secret},
					  success: function (data) { 
						  // console.log(data); 
						  if(data.code == true){
                            $.ajax({  
                            headers: {  
                                Accept: "application/json; charset=utf-8" ,
                                Authorization: token,
                                ID: admin_info.id
                            },  
                            type: "get",  
                            url: interfaceUrl + '/Mapi/DoOpenGoogle?id='+admin_info.id+"&secret="+secret,//{'secret':secret},
                            success: function (data) { 
                                // console.log(data); 
                                if (!data.code){
                                  layer.msg('服务器发生异常!');
                                    
                                }else{
                                  alert('谷歌身份验证设置成功');
                                    location.reload();
                                }
                            }
                        });
                          }else{
                            alert('身份验证失败');
                            location.reload();
                          }
					  },error:function(error){
						  console.log(error);
					  }

			  });
        // alert(code);
        // $.get(interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id,{'code':code,'secret':secret},function(data){
        //                                 if(data.code == true){
        //                                         $.get(interfaceUrl + '/Mapi/DoOpenGoogle?id='+admin_info.id,{'secret':secret},function(data){
        //                                           alert('谷歌身份验证设置成功');
        //                                           location.reload();
        //                                         });
        //                                 }else{
        //                                     alert('身份验证失败');
        //                                     location.reload();
        //                                 }
        //                         });
        
        
      })

      $('#close').click(function(){
        var code;
        code=prompt("请输入谷歌身份验证码"); 
        // alert(code);
        $.ajax({  
                headers: {  
                    Accept: "application/json; charset=utf-8" ,
                    Authorization: token,
                    ID: admin_info.id
                },  
                type: "get",  
                url: interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id+"&code="+code,//,{'code':code}
                success: function (data) { 
                    // console.log(data); 
                    if(data.success == 1){
                        $.ajax({  
                            headers: {  
                                Accept: "application/json; charset=utf-8" ,
                                Authorization: token,
                                ID: admin_info.id
                            },  
                            type: "get",  
                            url: interfaceUrl + '/Mapi/DoCloseGoogle?id='+admin_info.id,//,{'code':code}
                            success: function (data) { 
                                // console.log(data); 
                                if (!data.code){
                                    alert('谷歌身份验证关闭成功');
                                    location.reload();
                                }
                            }
                        });
                    }else{
                        alert('身份验证失败');
                        location.reload();
                    }
                    // if (!data.code){
                    //     localStorage.clear();
                    //     layer.msg('登录超时，请重新登录！');
                    //     window.location.href = '/login.html';
                    // }
                },error:function(error){
                    console.log(error);
                }

            });
        // $.get(interfaceUrl + '/Mapi/CheckGoogle?id='+admin_info.id,{'code':code},function(data){
        //                                 if(data.success == 1){
        //                                   $.get(interfaceUrl + '/Mapi/DoCloseGoogle?id='+admin_info.id,function(data){
        //                                           alert('谷歌身份验证已关闭');
        //                                           location.reload();
        //                                   });
        //                                 }else{
        //                                     alert('身份验证失败');
        //                                     location.reload();
        //                                 }
        //                         });
        
      })
  });
  </script>
  
</body>
</html>