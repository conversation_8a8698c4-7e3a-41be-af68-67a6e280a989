layui.define(["table","form","element"],function(e){var t=layui.$,i=layui.table,r=(layui.form,layui.element);i.render({elem:"#LAY-app-system-order",url:layui.setter.base+"json/workorder/demo.js",cols:[[{type:"numbers",fixed:"left"},{field:"orderid",width:100,title:"\u5de5\u5355\u53f7",sort:!0},{field:"attr",width:100,title:"\u4e1a\u52a1\u6027\u8d28"},{field:"title",width:100,title:"\u5de5\u5355\u6807\u9898",width:300},{field:"progress",title:"\u8fdb\u5ea6",width:200,align:"center",templet:"#progressTpl"},{field:"submit",width:100,title:"\u63d0\u4ea4\u8005"},{field:"accept",width:100,title:"\u53d7\u7406\u4eba\u5458"},{field:"state",title:"\u5de5\u5355\u72b6\u6001",templet:"#buttonTpl",minWidth:80,align:"center"},{title:"\u64cd\u4f5c",align:"center",fixed:"right",toolbar:"#table-system-order"}]],page:!0,limit:10,limits:[10,15,20,25,30],text:"\u5bf9\u4e0d\u8d77\uff0c\u52a0\u8f7d\u51fa\u73b0\u5f02\u5e38\uff01",done:function(){r.render("progress")}}),i.on("tool(LAY-app-system-order)",function(e){e.data;if("edit"===e.event){t(e.tr);layer.open({type:2,title:"\u7f16\u8f91\u5de5\u5355",content:"../../../views/app/workorder/listform.html",area:["450px","450px"],btn:["\u786e\u5b9a","\u53d6\u6d88"],yes:function(e,t){var r=window["layui-layer-iframe"+e],l="LAY-app-workorder-submit",o=t.find("iframe").contents().find("#"+l);r.layui.form.on("submit("+l+")",function(t){t.field;i.reload("LAY-user-front-submit"),layer.close(e)}),o.trigger("click")},success:function(e,t){}})}}),e("workorder",{})});