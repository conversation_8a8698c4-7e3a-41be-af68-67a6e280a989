[{"anonymous": false, "inputs": [{"indexed": true, "name": "maker", "type": "address"}, {"indexed": false, "name": "taker", "type": "address"}, {"indexed": true, "name": "feeRecipient", "type": "address"}, {"indexed": false, "name": "makerToken", "type": "address"}, {"indexed": false, "name": "takerToken", "type": "address"}, {"indexed": false, "name": "filledMakerTokenAmount", "type": "uint256"}, {"indexed": false, "name": "filledTakerTokenAmount", "type": "uint256"}, {"indexed": false, "name": "paidMakerFee", "type": "uint256"}, {"indexed": false, "name": "paidTakerFee", "type": "uint256"}, {"indexed": true, "name": "tokens", "type": "bytes32"}, {"indexed": false, "name": "orderHash", "type": "bytes32"}], "name": "LogFill", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "maker", "type": "address"}, {"indexed": true, "name": "feeRecipient", "type": "address"}, {"indexed": false, "name": "makerToken", "type": "address"}, {"indexed": false, "name": "takerToken", "type": "address"}, {"indexed": false, "name": "cancelledMakerTokenAmount", "type": "uint256"}, {"indexed": false, "name": "cancelledTakerTokenAmount", "type": "uint256"}, {"indexed": true, "name": "tokens", "type": "bytes32"}, {"indexed": false, "name": "orderHash", "type": "bytes32"}], "name": "LogCancel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "errorId", "type": "uint8"}, {"indexed": true, "name": "orderHash", "type": "bytes32"}], "name": "LogError", "type": "event"}]