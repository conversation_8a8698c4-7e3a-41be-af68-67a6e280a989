package gofiber

import (
	"Admin/internal/lib"
	"Admin/internal/task"
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

func NewFiberApp(logger *zap.Logger) *fiber.App {
	tm := time.Now()
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(9000) + 1000
	fmt.Println("CE" + tm.Format("060102150405") + cast.ToString(randomNumber))
	app := fiber.New()
	// 配置 CORS
	app.Use(func(c *fiber.Ctx) error {
		c.Set("Access-Control-Allow-Origin", "*")
		c.Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		c.Set("Access-Control-Allow-Headers", "Content-Type,Authorization")
		c.Set("Access-Control-Allow-Credentials", "true")

		// 处理 OPTIONS 预检请求
		if c.Method() == "OPTIONS" {
			return c.SendStatus(204)
		}

		return c.Next()
	})
	// 使用静态文件
	//app.Static("/", "D:\\GOWWW\\yuanshennew\\NewAdmin\\data")
	fmt.Println(lib.GetCurrentAbPathByExecutable() + "/data")
	app.Static("/", lib.GetCurrentAbPathByExecutable()+"/data")
	c := cron.New(cron.WithSeconds())
	c.AddFunc("05 00 0 * * *", func() {
		//task.TrcCollection()
		//task.ErcCollection()
		task.DoTieredRate()
	})
	c.Start()
	app.Use(CustomZapLogger(logger))
	app.Use(func(c *fiber.Ctx) error {
		ip := c.Get("CF-Connecting-IP")
		if ip != "" {
			c.Locals("ip", ip)
		} else {
			c.Locals("ip", c.IP())
		}
		return c.Next()
	})
	// 在这里注册路由
	SetupRoutes(app)

	return app
}

func RunFiberServer(app *fiber.App, lc fx.Lifecycle) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				app.Listen(":8888")
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return app.Shutdown()
		},
	})
}

func CustomZapLogger(logger *zap.Logger) fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()
		err := c.Next()
		stop := time.Now()

		logger.Info("Request",
			zap.String("method", c.Method()),
			zap.String("path", c.Path()),
			zap.String("ip", c.IP()),
			zap.String("user_agent", c.Get("User-Agent")),
			zap.Duration("duration", stop.Sub(start)),
			zap.Int("status", c.Response().StatusCode()),
		)

		return err
	}
}
