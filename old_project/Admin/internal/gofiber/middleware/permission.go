package middleware

import (
	"github.com/gofiber/fiber/v2"
)

// 验证权限逻辑
func DoPermission() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取用户信息
		_, ok := c.Locals("userid").(string)
		if !ok {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "未登录", "data": nil})
		}

		//admins_role, aerr := dal.AdminRole.Where(dal.AdminRole.Adminid.Eq(cast.ToInt32(userid))).Where(dal.AdminRole.DelFlg.Eq(0)).First()
		//if admins_role == nil || aerr != nil {
		//	return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "权限不足", "data": nil})
		//}
		//log.Printf("admins_role:%v", c.Path())
		//permission, perr := dal.Permission.Where(dal.Permission.Route.Eq(c.Path())).Where(dal.Permission.DelFlg.Eq(0)).First()
		//if permission == nil || perr != nil {
		//	return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "权限不足", "data": nil})
		//}
		//log.Printf("admins_role:%v", *admins_role.RoleID)
		//log.Printf("admins_role:%v", permission.ID)
		//role_per, rerr := dal.RolePermission.Where(dal.RolePermission.Roleid.Eq(*admins_role.RoleID)).Where(dal.RolePermission.Permissionsid.Eq(permission.ID)).Where(dal.RolePermission.DelFlg.Eq(0)).First()
		//if role_per == nil || rerr != nil {
		//	return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "权限不足", "data": nil})
		//}
		return c.Next()
	}
}
