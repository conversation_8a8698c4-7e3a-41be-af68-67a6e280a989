package middleware

import (
	"Admin/config"
	"fmt"
	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v4"
	"strings"
)

// Jwt验证逻辑
func JwtMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		tokenStr := c.Get("Authorization")

		if strings.HasPrefix(tokenStr, "Bearer ") {
			tokenStr = strings.TrimPrefix(tokenStr, "Bearer ")
		}

		token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(config.NewConfig().Jwt.Secret), nil
		})

		if err != nil {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "Token无效", "data": nil})
		}

		if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
			c.Locals("username", claims["username"])
			c.Locals("userid", claims["userid"])
		} else {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{"code": false, "msg": "Token无效", "data": nil})
		}

		return c.Next()
	}
}

// JwtToken生成
func CreateToken(username string, userid string) (string, error) {
	token := jwt.New(jwt.SigningMethodHS256)

	claims := token.Claims.(jwt.MapClaims)
	claims["username"] = username
	claims["userid"] = userid

	t, err := token.SignedString([]byte(config.NewConfig().Jwt.Secret))
	if err != nil {
		return "", err
	}
	return t, nil
}
