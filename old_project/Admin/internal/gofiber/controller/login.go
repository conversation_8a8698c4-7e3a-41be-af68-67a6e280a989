package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

type LoginRequest struct {
	Username string `form:"username" validate:"required"`
	Password string `form:"password" validate:"required"`
}

var validate *validator.Validate

// 登录
func DoLogin(c *fiber.Ctx) error {
	request := new(LoginRequest)
	if err := c.BodyParser(request); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code": false,
			"msg":  "Bad Request",
			"data": nil,
		})
	}
	validate = validator.New()
	// 参数校验
	if err := validate.Struct(request); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"code": false,
			"msg":  "username password 必传",
			"data": nil,
		})
	}
	res := service.DoLogin(c)
	return c.Status(fiber.StatusOK).JSON(res)
}

// 退出登录
func DoLogout(c *fiber.Ctx) error {
	res := service.DoLogout(c)
	return c.Status(fiber.StatusOK).JSON(res)
}
