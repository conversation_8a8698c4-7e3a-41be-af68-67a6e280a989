package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取地址列表
func GetAddressList(c *fiber.Ctx) error {
	return service.GetAddressList(c)
}

// 获取地址详情
func GetAddressInfo(c *fiber.Ctx) error {
	return service.GetAddressInfo(c)
}

// 删除地址
func DeleteAddress(c *fiber.Ctx) error {
	return service.DeleteAddress(c)
}

// 执行归集
func DoCollection(c *fiber.Ctx) error {
	return service.DoCollection(c)
}
