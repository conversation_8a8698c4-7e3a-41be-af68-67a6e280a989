package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取管理员列表
func GetAdminList(c *fiber.Ctx) error {
	return service.GetAdminList(c)
}

// 获取管理员详情
func GetAdminInfo(c *fiber.Ctx) error {
	return service.GetAdminInfo(c)
}

// 删除管理员
func DeleteAdmin(c *fiber.Ctx) error {
	return service.DeleteAdmin(c)
}

// 添加管理员
func InsertAdmin(c *fiber.Ctx) error {
	return service.InsertAdmin(c)
}

// 修改管理员
func UpdateAdmin(c *fiber.Ctx) error {
	return service.UpdateAdmin(c)
}

// 改密
func UpdatePassword(c *fiber.Ctx) error {
	return service.UpdatePassword(c)
}

// 修改管理员
func ResetPassword(c *fiber.Ctx) error {
	return service.ResetPassword(c)
}

// 获取后台日志列表
func GetAdminLogList(c *fiber.Ctx) error {
	return service.GetAdminLogList(c)
}
