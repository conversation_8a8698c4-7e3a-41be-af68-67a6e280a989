package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取充值订单列表
func GetOrderList(c *fiber.Ctx) error {
	return service.GetOrderList(c)
}

// 获取充值订单详情
func GetOrderInfo(c *fiber.Ctx) error {
	return service.GetOrderInfo(c)
}

// 修改充值订单状态
func UpdateOrder(c *fiber.Ctx) error {
	return service.UpdateOrder(c)
}

// 补发充值订单回调
func DoCallbackOrder(c *fiber.Ctx) error {
	return service.DoCallbackOrder(c)
}

// 创建空单
func CreateEmptyOrder(c *fiber.Ctx) error {
	return service.CreateEmptyOrder(c)
}
