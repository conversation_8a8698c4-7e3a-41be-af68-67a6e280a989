package controller

import (
	"Admin/internal/gofiber/service"
	"github.com/gofiber/fiber/v2"
)

// 获取下发订单列表
func GetPayOrderList(c *fiber.Ctx) error {
	return service.GetPayOrderList(c)
}

// 获取下发订单详情
func GetPayOrderInfo(c *fiber.Ctx) error {
	return service.GetPayOrderInfo(c)
}

// 修改下发订单状态
func UpdatePayOrder(c *fiber.Ctx) error {
	return service.UpdatePayOrder(c)
}

// 补发下发订单回调
func DoCallbackPayOrder(c *fiber.Ctx) error {
	return service.DoCallbackPayOrder(c)
}
