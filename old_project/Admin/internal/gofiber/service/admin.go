package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gen"
	"time"
)

type AdminList struct {
	ID       int32   `gorm:"column:id;type:int(11) unsigned;primaryKey;autoIncrement:true" json:"id"`
	Username *string `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Password *string `gorm:"column:password;type:varchar(255);comment:密码" json:"password"`
	Status   *int32  `gorm:"column:status;type:int(11);comment:0 启用 1 禁用" json:"status"`
	Ctime    *int32  `gorm:"column:ctime;type:int(30)" json:"ctime"`
	DelFlg   *int32  `gorm:"column:del_flg;type:int(11)" json:"del_flg"`
	Name     *string `json:"name"`
}

// 获取管理员列表
func GetAdminList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	where = append(where, dal.Admin.DelFlg.Eq(0))
	username := c.FormValue("username")
	if username != "" {
		where = append(where, dal.Admin.Username.Eq(username))
	}
	status := c.FormValue("status")
	if status != "" {
		where = append(where, dal.Admin.Status.Eq(cast.ToInt32(status)))
	}
	var list []AdminList
	dal.Admin.Select(dal.Admin.ALL, dal.Role.Name).Where(where...).Order(dal.Admin.ID.Desc()).LeftJoin(dal.AdminRole, dal.Admin.ID.EqCol(dal.AdminRole.Adminid)).LeftJoin(dal.Role, dal.AdminRole.RoleID.EqCol(dal.Role.ID)).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Scan(&list)
	count, _ := dal.Admin.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取管理员详情
func GetAdminInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	admin, _ := dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 0,
		"msg":  "成功",
		"data": admin,
	})
}

// 删除管理员
func DeleteAdmin(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(id))).Update(dal.Admin.DelFlg, 1)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 添加管理员
func InsertAdmin(c *fiber.Ctx) error {
	username := c.FormValue("username")
	password := c.FormValue("password")
	roleid := c.FormValue("role")
	check, _ := dal.Admin.Where(dal.Admin.Username.Eq(username)).Count()
	if check > 0 {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "用户名已存在",
			"data": nil,
		})
	}
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}
	pyd := string(hash)
	dal.Admin.Create(&entity.Admin{
		Username: &username,
		Password: &pyd,
		Status:   lib.Int64ToInt32Ptr(0),
		Ctime:    lib.Int64ToInt32Ptr(time.Now().Unix()),
		DelFlg:   lib.Int64ToInt32Ptr(0),
	})
	if roleid != "" {
		role, rerr := dal.Role.Where(dal.Role.ID.Eq(cast.ToInt32(roleid))).First()
		if role == nil || rerr != nil {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "失败",
				"data": nil,
			})
		}
		newadmin, _ := dal.Admin.Last()
		dal.AdminRole.Create(&entity.AdminRole{
			Adminid: &newadmin.ID,
			RoleID:  &role.ID,
			Ctime:   lib.Int64ToInt32Ptr(time.Now().Unix()),
			DelFlg:  lib.Int64ToInt32Ptr(0),
		})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 修改管理员
func UpdateAdmin(c *fiber.Ctx) error {
	id := c.FormValue("id")
	status := c.FormValue("status")
	dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(id))).Update(dal.Admin.Status, status)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 改密
func UpdatePassword(c *fiber.Ctx) error {
	id := c.FormValue("id")
	password := c.FormValue("password")
	hash, herr := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if herr != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}
	pwd := string(hash)
	dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(id))).Update(dal.Admin.Password, pwd)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 修改密码
func ResetPassword(c *fiber.Ctx) error {
	userid, ok := c.Locals("userid").(string)
	if !ok {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}
	admin, _ := dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(userid))).First()
	oldpassword := c.FormValue("oldpwd")
	newpassword := c.FormValue("newpwd")
	err := bcrypt.CompareHashAndPassword([]byte(*admin.Password), []byte(oldpassword))
	if err != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "原密码错误",
			"data": nil,
		})
	}
	if oldpassword == newpassword {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "新密码不能与原密码相同",
			"data": nil,
		})
	}
	confirmpassword := c.FormValue("confirmpwd")
	if confirmpassword != newpassword {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "两次输入的密码不一致",
			"data": nil,
		})
	}

	hash, herr := bcrypt.GenerateFromPassword([]byte(newpassword), bcrypt.DefaultCost)
	if herr != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}

	pwd := string(hash)

	dal.Admin.Where(dal.Admin.ID.Eq(cast.ToInt32(userid))).Update(dal.Admin.Password, pwd)

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 获取后台操作记录列表
func GetAdminLogList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	username := c.FormValue("username")
	if username != "" {
		where = append(where, dal.Adminslog.Username.Eq(username))
	}
	start_time := c.FormValue("start_time")
	end_time := c.FormValue("end_time")
	if start_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", start_time, time.Local)
		where = append(where, dal.Adminslog.Ctime.Gt(cast.ToInt32(tres.Unix())))
	}
	if end_time != "" {
		tres, _ := time.ParseInLocation("2006-01-02 15:04:05", end_time, time.Local)
		where = append(where, dal.Adminslog.Ctime.Lt(cast.ToInt32(tres.Unix())))
	}
	list, _ := dal.Adminslog.Where(where...).Order(dal.Adminslog.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Adminslog.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}
