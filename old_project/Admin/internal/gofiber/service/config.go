package service

import (
	"Admin/dal"
	"github.com/gofiber/fiber/v2"
	"strings"
)

// 获取网站配置
func GetConfigInfo(c *fiber.Ctx) error {
	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TrcTranFee")).First()
	col, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ColAmount")).First()
	data := make(map[string]string)
	arr := strings.Split(cfg.Value, ",")
	data["trcfee1"] = arr[0]
	data["trcfee2"] = arr[1]
	data["col"] = col.Value
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": data,
	})
}

// 修改网站配置
func UpdateConfig(c *fiber.Ctx) error {
	trc1 := c.FormValue("trc1")
	trc2 := c.FormValue("trc2")
	col := c.FormValue("col")
	str := trc1 + "," + trc2
	dal.Cfg.Where(dal.Cfg.Func.Eq("TrcTranFee")).Update(dal.Cfg.Value, str)
	dal.Cfg.Where(dal.Cfg.Func.Eq("ColAmount")).Update(dal.Cfg.Value, col)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "OK",
		"data": nil,
	})
}
