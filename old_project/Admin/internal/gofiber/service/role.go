package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"time"
)

// 获取角色列表
func GetRoleList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	where = append(where, dal.Role.DelFlg.Eq(0))
	name := c.FormValue("name")
	if name != "" {
		where = append(where, dal.Role.Name.Eq(name))
	}
	list, _ := dal.Role.Where(where...).Order(dal.Role.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Role.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取角色详情
func GetRoleInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	role, _ := dal.Role.Where(dal.Role.ID.Eq(cast.ToInt32(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": role,
	})
}

// 删除角色
func DeleteRole(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Role.Where(dal.Role.ID.Eq(cast.ToInt32(id))).Update(dal.Role.DelFlg, 1)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 添加角色
func InsertRole(c *fiber.Ctx) error {
	name := c.FormValue("name")
	description := c.FormValue("description")
	dal.Role.Create(&entity.Role{
		Name:        &name,
		Description: &description,
		Ctime:       lib.Int64ToInt32Ptr(time.Now().Unix()),
		DelFlg:      lib.Int64ToInt32Ptr(0),
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 修改角色
func UpdateRole(c *fiber.Ctx) error {
	name := c.FormValue("name")
	description := c.FormValue("description")
	id := c.FormValue("id")
	dal.Role.Where(dal.Role.ID.Eq(cast.ToInt32(id))).Updates(&entity.Role{
		Name:        &name,
		Description: &description,
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "OK",
		"data": nil,
	})
}
