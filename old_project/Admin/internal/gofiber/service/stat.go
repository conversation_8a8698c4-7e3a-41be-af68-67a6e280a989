package service

import (
	"Admin/dal"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
	"time"
)

// 获取主页统计数据
func GetConsole(c *fiber.Ctx) error {
	// 获取当前时间
	now := time.Now()

	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endOfDay := startOfDay.AddDate(0, 0, 1).Add(-time.Nanosecond)

	startOfLast7Days := startOfDay.AddDate(0, 0, -6)
	endOfLast7Days := endOfDay

	startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)

	startOfLastMonth := time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())

	endOfLastMonth := startOfLastMonth.AddDate(0, 1, 0).Add(-time.Nanosecond)
	//当天跑量
	daysql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'daymoney' from `order` where ctime>" + cast.ToString(startOfDay.Unix()) + " and ctime<" + cast.ToString(endOfDay.Unix()) + " and status in (2,3) and st = 1"
	dayresult := make(map[string]interface{})
	dal.DB.Raw(daysql).Scan(&dayresult)
	DaySuccessMoney := dayresult["daymoney"]
	//近7日跑量
	weeksql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'weekmoney' from `order` where ctime>" + cast.ToString(startOfLast7Days.Unix()) + " and ctime<" + cast.ToString(endOfLast7Days.Unix()) + " and status in (2,3) and st = 1"
	weekresult := make(map[string]interface{})
	dal.DB.Raw(weeksql).Scan(&weekresult)
	WeekSuccessMoney := weekresult["weekmoney"]
	//本月跑量
	monthsql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'monthmoney' from `order` where ctime>" + cast.ToString(startOfMonth.Unix()) + " and ctime<" + cast.ToString(endOfMonth.Unix()) + " and status in (2,3) and st = 1"
	monthresult := make(map[string]interface{})
	dal.DB.Raw(monthsql).Scan(&monthresult)
	MonthSuccessMoney := monthresult["monthmoney"]
	//上月跑量
	lastmonthsql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'lastmonthmoney' from `order` where ctime>" + cast.ToString(startOfLastMonth.Unix()) + " and ctime<" + cast.ToString(endOfLastMonth.Unix()) + " and status in (2,3) and st = 1"
	lastmonthresult := make(map[string]interface{})
	dal.DB.Raw(lastmonthsql).Scan(&lastmonthresult)
	LastMonthSuccessMoney := lastmonthresult["lastmonthmoney"]
	//当天盈利
	dayfeesql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'dayfeemoney' from `trans` where ctime>" + cast.ToString(startOfDay.Unix()) + " and ctime<" + cast.ToString(endOfDay.Unix()) + " and type in (2,6,16) and st = 1"
	dayfeeresult := make(map[string]interface{})
	dal.DB.Raw(dayfeesql).Scan(&dayfeeresult)
	DayFeeMoney := dayfeeresult["dayfeemoney"]
	//近7日盈利
	weekfeesql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'weekfeemoney' from `trans` where ctime>" + cast.ToString(startOfLast7Days.Unix()) + " and ctime<" + cast.ToString(endOfLast7Days.Unix()) + " and type in (2,6,16) and st = 1"
	weekfeeresult := make(map[string]interface{})
	dal.DB.Raw(weekfeesql).Scan(&weekfeeresult)
	WeekFeeMoney := weekfeeresult["weekfeemoney"]
	//本月盈利
	monthfeesql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'monthfeemoney' from `trans` where ctime>" + cast.ToString(startOfMonth.Unix()) + " and ctime<" + cast.ToString(endOfMonth.Unix()) + " and type in (2,6,16) and st = 1"
	monthfeeresult := make(map[string]interface{})
	dal.DB.Raw(monthfeesql).Scan(&monthfeeresult)
	MonthFeeMoney := monthfeeresult["monthfeemoney"]
	//上月盈利
	lastmonthfeesql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'lastmonthfeemoney' from `trans` where ctime>" + cast.ToString(startOfLastMonth.Unix()) + " and ctime<" + cast.ToString(endOfLastMonth.Unix()) + " and type in (2,6,16) and st = 1"
	lastmonthfeeresult := make(map[string]interface{})
	dal.DB.Raw(lastmonthfeesql).Scan(&lastmonthfeeresult)
	LastMonthFeeMoney := lastmonthfeeresult["lastmonthfeemoney"]
	//当天收款数
	daycsql := "select COUNT(*)as 'dayc' from `order` where ctime>" + cast.ToString(startOfDay.Unix()) + " and ctime<" + cast.ToString(endOfDay.Unix()) + " and status in (2,3) and st = 1"
	daycresult := make(map[string]interface{})
	dal.DB.Raw(daycsql).Scan(&daycresult)
	DaySuccessC := daycresult["dayc"]
	//近7日收款数
	weekcsql := "select COUNT(*)as 'weekc' from `order` where ctime>" + cast.ToString(startOfLast7Days.Unix()) + " and ctime<" + cast.ToString(endOfLast7Days.Unix()) + " and status in (2,3) and st = 1"
	weekcresult := make(map[string]interface{})
	dal.DB.Raw(weekcsql).Scan(&weekcresult)
	WeekSuccessC := weekcresult["weekc"]
	//本月收款数
	monthcsql := "select COUNT(*)as 'monthc' from `order` where ctime>" + cast.ToString(startOfMonth.Unix()) + " and ctime<" + cast.ToString(endOfMonth.Unix()) + " and status in (2,3) and st = 1"
	monthcresult := make(map[string]interface{})
	dal.DB.Raw(monthcsql).Scan(&monthcresult)
	MonthSuccessC := monthcresult["monthc"]
	//上月收款数
	lastmonthcsql := "select COUNT(*)as 'lastmonthc' from `order` where ctime>" + cast.ToString(startOfLastMonth.Unix()) + " and ctime<" + cast.ToString(endOfLastMonth.Unix()) + " and status in (2,3) and st = 1"
	lastmonthcresult := make(map[string]interface{})
	dal.DB.Raw(lastmonthcsql).Scan(&lastmonthcresult)
	LastMonthSuccessC := lastmonthcresult["lastmonthc"]
	//当天下发数
	daypcsql := "select COUNT(*)as 'daypc' from `payorder` where ctime>" + cast.ToString(startOfDay.Unix()) + " and ctime<" + cast.ToString(endOfDay.Unix()) + " and status in (2,3)"
	daypcresult := make(map[string]interface{})
	dal.DB.Raw(daypcsql).Scan(&daypcresult)
	DaySuccessPC := daypcresult["daypc"]
	//近7日下发数
	weekpcsql := "select COUNT(*)as 'weekpc' from `payorder` where ctime>" + cast.ToString(startOfLast7Days.Unix()) + " and ctime<" + cast.ToString(endOfLast7Days.Unix()) + " and status in (2,3)"
	weekpcresult := make(map[string]interface{})
	dal.DB.Raw(weekpcsql).Scan(&weekpcresult)
	WeekSuccessPC := weekpcresult["weekpc"]
	//本月下发数
	monthpcsql := "select COUNT(*)as 'monthpc' from `payorder` where ctime>" + cast.ToString(startOfMonth.Unix()) + " and ctime<" + cast.ToString(endOfMonth.Unix()) + " and status in (2,3)"
	monthpcresult := make(map[string]interface{})
	dal.DB.Raw(monthpcsql).Scan(&monthpcresult)
	MonthSuccessPC := monthpcresult["monthpc"]
	//上月下发数
	lastmonthpcsql := "select COUNT(*)as 'lastmonthpc' from `payorder` where ctime>" + cast.ToString(startOfLastMonth.Unix()) + " and ctime<" + cast.ToString(endOfLastMonth.Unix()) + " and status in (2,3)"
	lastmonthpcresult := make(map[string]interface{})
	dal.DB.Raw(lastmonthpcsql).Scan(&lastmonthpcresult)
	LastMonthSuccessPC := lastmonthpcresult["lastmonthpc"]
	data := make(map[string]interface{})
	data["DaySuccessMoney"] = DaySuccessMoney
	data["WeekSuccessMoney"] = WeekSuccessMoney
	data["MonthSuccessMoney"] = MonthSuccessMoney
	data["LastMonthSuccessMoney"] = LastMonthSuccessMoney
	data["DayFeeMoney"] = DayFeeMoney
	data["WeekFeeMoney"] = WeekFeeMoney
	data["MonthFeeMoney"] = MonthFeeMoney
	data["LastMonthFeeMoney"] = LastMonthFeeMoney
	data["DaySuccessC"] = DaySuccessC
	data["WeekSuccessC"] = WeekSuccessC
	data["MonthSuccessC"] = MonthSuccessC
	data["LastMonthSuccessC"] = LastMonthSuccessC
	data["DaySuccessPC"] = DaySuccessPC
	data["WeekSuccessPC"] = WeekSuccessPC
	data["MonthSuccessPC"] = MonthSuccessPC
	data["LastMonthSuccessPC"] = LastMonthSuccessPC
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 0,
		"msg":  "成功",
		"data": data,
	})
}
