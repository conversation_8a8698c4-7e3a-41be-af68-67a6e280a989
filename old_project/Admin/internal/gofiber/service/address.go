package service

import (
	"Admin/config"
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/internal/task"
	"Admin/internal/transfer"
	"Admin/model/entity"
	"fmt"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"strings"
)

// 获取地址列表
func GetAddressList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	where = append(where, dal.Address.DelFlg.Eq(0))
	mchname := c.FormValue("mchname")
	if mchname != "" {
		where = append(where, dal.Address.MerchantName.Eq(mchname))
	}
	trc := c.FormValue("trc")
	if trc != "" {
		where = append(where, dal.Address.Trxaddr.Eq(trc))
	}
	erc := c.FormValue("erc")
	if erc != "" {
		where = append(where, dal.Address.Ethaddr.Eq(erc))
	}
	list, _ := dal.Address.Where(where...).Order(dal.Address.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Address.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取地址详情
func GetAddressInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": 0,
		"msg":  "成功",
		"data": address,
	})
}

// 删除地址
func DeleteAddress(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(id))).Update(dal.Address.DelFlg, 1)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 执行归集
func DoCollection(c *fiber.Ctx) error {
	idstr := c.FormValue("ids")
	fmt.Println(idstr)
	ids := strings.Split(idstr, ",")
	var idarr []int64
	for _, id := range ids {
		idarr = append(idarr, cast.ToInt64(id))
	}
	//执行TRC归集
	address, aerr := dal.Address.Where(dal.Address.ID.In(idarr...)).Where(dal.Address.UsdtTrc.Gt(0)).Find()
	if address == nil || aerr != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "失败",
			"data": nil,
		})
	}
	for _, v := range address {
		if *v.Lock == 1 {
			continue
		}
		trx, trxe := transfer.QueryTron(*v.Trxaddr)
		if trxe != nil {
			fmt.Println(trxe)
			continue
		}
		if trx == 0 {
			continue
		}
		//租能量
		response, lerr := task.DoLease(*v.Trxaddr, "1", "32000", config.NewConfig().LeaseApi.Key)
		if lerr != nil {
			fmt.Println("Error:", lerr)
			continue
		}
		if response.Status != 200 {
			fmt.Println(response)
			continue
		}
		orderno := cast.ToString(response.Data.OrderNo)
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Trxlock: lib.Int64ToInt32Ptr(1), Leaseid: &orderno})
	}
	//执行ERC归集
	ercaddress, _ := dal.Address.Where(dal.Address.ID.In(idarr...)).Where(dal.Address.UsdtErc.Gt(0)).Find()
	trccfg := config.NewConfig().Addr.Trc
	erccfg := config.NewConfig().Addr.Erc
	keycfg := config.NewConfig().Addr.Key
	kk := lib.Dok(trccfg, erccfg, keycfg)
	for _, e := range ercaddress {
		if *e.Ethlock == 1 {
			continue
		}
		if *e.Eth >= 0.002 {
			continue
		}
		cha, _ := decimal.NewFromFloat(0.002).Sub(decimal.NewFromFloat(*e.Eth)).Float64()
		hash, terr := transfer.EthSend(kk, *e.Ethaddr, cha)
		if terr != nil {
			fmt.Println(*e.Ethaddr + " 归集转账ETH时报错")
			fmt.Println(terr)
			continue
		}
		kong := ""
		dal.Address.Where(dal.Address.ID.Eq(e.ID)).Updates(&entity.Address{Ethlock: lib.Int64ToInt32Ptr(1), Ethhash: &hash, Usdthash: &kong})
	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
