package service

import (
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/model/entity"
	"github.com/gofiber/fiber/v2"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"strconv"
	"time"
)

// 获取充值订单列表
func GetOrderList(c *fiber.Ctx) error {
	page := c.FormValue("page")
	intpage := lib.StrToint64(page)
	limit := c.FormValue("limit")
	intlimit := lib.StrToint64(limit)
	var offset int64 = 0
	if intpage == 1 {
		offset = 0
	} else {
		offset = (intpage - 1) * intlimit
	}
	var where []gen.Condition
	orderid := c.FormValue("orderid")
	rorderid := c.FormValue("rorderid")
	status := c.FormValue("status")
	trxadd := c.FormValue("trxadd")
	chain := c.FormValue("chain")
	if orderid != "" {
		where = append(where, dal.Order.Orderid.Eq(orderid))
	}
	if rorderid != "" {
		where = append(where, dal.Order.Rorderid.Eq(rorderid))
	}
	if trxadd != "" {
		where = append(where, dal.Order.Trxadd.Eq(trxadd))
	}
	if chain != "" {
		where = append(where, dal.Order.Chain.Eq(chain))
	}
	if status != "" {
		if status == "2" {
			where = append(where, dal.Order.Status.In(2, 3))
		} else if status == "6" {
			where = append(where, dal.Order.Status.Eq(0))
			where = append(where, dal.Order.Realmoney.IsNotNull())
			where = append(where, dal.Order.Transactionid.IsNotNull())
		} else if status == "0" {
			where = append(where, dal.Order.Status.Eq(0))
			where = append(where, dal.Order.Realmoney.IsNull())
			where = append(where, dal.Order.Transactionid.IsNull())
		} else {
			where = append(where, dal.Order.Status.Eq(cast.ToInt32(status)))
		}
	}
	where = append(where, dal.Order.St.Eq(1))
	list, _ := dal.Order.Where(where...).Order(dal.Order.ID.Desc()).Limit(cast.ToInt(limit)).Offset(cast.ToInt(offset)).Find()
	count, _ := dal.Order.Where(where...).Count()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code":  0,
		"count": count,
		"data":  list,
	})
}

// 获取充值订单详情
func GetOrderInfo(c *fiber.Ctx) error {
	id := c.FormValue("id")
	order, _ := dal.Order.Where(dal.Order.ID.Eq(cast.ToInt64(id))).First()
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": order,
	})
}

// 修改充值订单状态
func UpdateOrder(c *fiber.Ctx) error {
	id := c.FormValue("id")
	status := c.FormValue("status")
	order, _ := dal.Order.Where(dal.Order.ID.Eq(cast.ToInt64(id))).First()
	if order == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
			"data": nil,
		})
	}
	if status == "5" {
		if *order.Status > 0 {
			return c.Status(fiber.StatusOK).JSON(fiber.Map{
				"code": false,
				"msg":  "订单已支付或已完成,不可以取消!",
				"data": nil,
			})
		}
		dal.Order.Where(dal.Order.ID.Eq(order.ID)).Updates(&entity.Order{
			Status: lib.Int64ToInt32Ptr(5),
			Etime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(*order.Addrid)).Where(dal.Lockaddress.Islock.Eq(0)).Where(dal.Lockaddress.Money.Eq(*order.Money)).Update(dal.Lockaddress.Islock, 0)
		dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(*order.Addrid))).Update(dal.Address.Lock, 0)
	} else if status == "2" {
		realmoney := c.FormValue("realmoney")
		m := realmoney
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(*order.MerchantID))).First()
		txid := "手动成功"
		emoney64, _ := strconv.ParseFloat(m, 64)
		//更新订单状态
		dal.Order.Where(dal.Order.ID.Eq(order.ID)).Updates(&entity.Order{
			Transactionid: &txid,
			Status:        lib.Int64ToInt32Ptr(2),
			Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			Realmoney:     &m,
		})
		address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(order.Addrid))).First()
		//解锁地址
		dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Where(dal.Lockaddress.Money.Eq(*order.Money)).Update(dal.Lockaddress.Islock, 1)
		dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
		if *order.Chain == "Trc20" {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Fee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.Money, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          &madd,
				Erc:          mch.ErcMoney,
				Bsc:          mch.BscMoney,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(1)),
				Money:         &ying64,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(2)),
				Money:         &mchfee,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Fee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Money)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Money, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(1)),
					Money:         &yingagadd,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: order.Transactionid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		} else if *order.Chain == "Erc20" {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Ethfee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.ErcMoney)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcMoney, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          mch.Money,
				Erc:          &madd,
				Bsc:          mch.BscMoney,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(5)),
				Money:         &ying64,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(6)),
				Money:         &mchfee,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Ethfee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Ercmoney)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Ercmoney, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(2)),
					Money:         &yingagadd,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: order.Transactionid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		} else {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Bscfee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.BscMoney)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.BscMoney, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          mch.Money,
				Erc:          mch.ErcMoney,
				Bsc:          &madd,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(15)),
				Money:         &ying64,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(16)),
				Money:         &mchfee,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: &txid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Bscfee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Bscmoney)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Bscmoney, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(3)),
					Money:         &yingagadd,
					Orderid:       order.Orderid,
					Rorderid:      order.Rorderid,
					Transactionid: order.Transactionid,
					Username:      mch.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}

	}
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 补发充值订单回调
func DoCallbackOrder(c *fiber.Ctx) error {
	id := c.FormValue("id")
	dal.Order.Where(dal.Order.ID.Eq(cast.ToInt64(id))).Updates(&entity.Order{
		Status:      lib.Int64ToInt32Ptr(2),
		Callbacknum: lib.Int64ToInt32Ptr(0),
	})
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}

// 创建空单
func CreateEmptyOrder(c *fiber.Ctx) error {
	id := c.FormValue("id")
	money := c.FormValue("money")
	order, oerr := dal.Order.Where(dal.Order.ID.Eq(cast.ToInt64(id))).First()
	if order == nil || oerr != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单不存在",
			"data": nil,
		})
	}
	if *order.Status != 0 || order.Transactionid == nil || order.Realmoney == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "订单并不是入账待处理状态",
			"data": nil,
		})
	}
	neworderid := *order.Orderid + "(已补空单)"
	newrorderid := *order.Rorderid + "(已补空单)"
	dal.Order.Where(dal.Order.ID.Eq(cast.ToInt64(id))).Updates(&entity.Order{
		Orderid:  &neworderid,
		Rorderid: &newrorderid,
		Status:   lib.Int64ToInt32Ptr(5),
	})
	dal.Order.Create(&entity.Order{
		CreatedAt:      order.CreatedAt,
		UpdatedAt:      order.UpdatedAt,
		DeletedAt:      order.DeletedAt,
		MerchantID:     order.MerchantID,
		Appkey:         order.Appkey,
		Transactionid:  order.Transactionid,
		Orderid:        order.Orderid,
		Rorderid:       order.Rorderid,
		Trxadd:         order.Trxadd,
		Addrid:         order.Addrid,
		Money:          &money,
		Status:         lib.Int64ToInt32Ptr(2),
		Callbackurl:    order.Callbackurl,
		Callbacknum:    lib.Int64ToInt32Ptr(0),
		Callbackresult: nil,
		MerchantName:   order.MerchantName,
		Ctime:          order.Ctime,
		Etime:          lib.Int64ToInt32Ptr(time.Now().Unix()),
		Returnurl:      order.Returnurl,
		Rmb:            order.Rmb,
		Rate:           order.Rate,
		Realmoney:      &money,
		Chain:          order.Chain,
	})

	m := money
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(*order.MerchantID))).First()
	emoney64, _ := strconv.ParseFloat(m, 64)
	address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(order.Addrid))).First()
	//解锁地址
	dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Where(dal.Lockaddress.Money.Eq(*order.Money)).Update(dal.Lockaddress.Islock, 1)
	dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
	if *order.Chain == "Trc20" {
		//商户余额上分
		m64, _ := strconv.ParseFloat(m, 64)
		//商户手续费处理
		demoney64 := decimal.NewFromFloat(emoney64)
		dmchfee64 := decimal.NewFromFloat(*mch.Fee)
		d1000 := decimal.NewFromInt(1000)
		//订单金额*商户费率/1000=商户手续费
		mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
		mchfee, _ := mchfeed.RoundCeil(6).Float64()
		ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
		madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.Money, madd)
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          &madd,
			Erc:          mch.ErcMoney,
			Bsc:          mch.BscMoney,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
		//商户余额变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(1)),
			Money:         &ying64,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Remark:        &str,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//商户手续费扣除
		//newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
		//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
		//商户手续费变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(2)),
			Money:         &mchfee,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//如果有代理 执行代理提成
		if *mch.AgentID != 0 {
			agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
			//demoney64 := decimal.NewFromFloat(emoney64)
			dagfee64 := decimal.NewFromFloat(*agent.Fee)
			//d1000 := decimal.NewFromInt(1000)
			//订单金额*代理费率/1000=代理提成
			agfeed := demoney64.Mul(dagfee64).Div(d1000)
			yingagadd, _ := mchfeed.Sub(agfeed).Float64()
			aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Money)).Float64()
			dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Money, aadd)
			//商户余额变动记录
			dal.AgentTran.Create(&entity.AgentTran{
				UserID:        lib.Int64ToInt32Ptr(agent.ID),
				Type:          lib.Int64ToInt32Ptr(int64(1)),
				Money:         &yingagadd,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: order.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
		}
	} else if *order.Chain == "Erc20" {
		//商户余额上分
		m64, _ := strconv.ParseFloat(m, 64)
		//商户手续费处理
		demoney64 := decimal.NewFromFloat(emoney64)
		dmchfee64 := decimal.NewFromFloat(*mch.Ethfee)
		d1000 := decimal.NewFromInt(1000)
		//订单金额*商户费率/1000=商户手续费
		mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
		mchfee, _ := mchfeed.RoundCeil(6).Float64()
		ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
		madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.ErcMoney)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcMoney, madd)
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          mch.Money,
			Erc:          &madd,
			Bsc:          mch.BscMoney,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
		//商户余额变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(5)),
			Money:         &ying64,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Remark:        &str,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//商户手续费扣除
		//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
		//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
		//商户手续费变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(6)),
			Money:         &mchfee,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//如果有代理 执行代理提成
		if *mch.AgentID != 0 {
			agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
			//demoney64 := decimal.NewFromFloat(emoney64)
			dagfee64 := decimal.NewFromFloat(*agent.Ethfee)
			//d1000 := decimal.NewFromInt(1000)
			//订单金额*代理费率/1000=代理提成
			agfeed := demoney64.Mul(dagfee64).Div(d1000)
			yingagadd, _ := mchfeed.Sub(agfeed).Float64()
			aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Ercmoney)).Float64()
			dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Ercmoney, aadd)
			//商户余额变动记录
			dal.AgentTran.Create(&entity.AgentTran{
				UserID:        lib.Int64ToInt32Ptr(agent.ID),
				Type:          lib.Int64ToInt32Ptr(int64(2)),
				Money:         &yingagadd,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: order.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
		}
	} else {
		//商户余额上分
		m64, _ := strconv.ParseFloat(m, 64)
		//商户手续费处理
		demoney64 := decimal.NewFromFloat(emoney64)
		dmchfee64 := decimal.NewFromFloat(*mch.Bscfee)
		d1000 := decimal.NewFromInt(1000)
		//订单金额*商户费率/1000=商户手续费
		mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
		mchfee, _ := mchfeed.RoundCeil(6).Float64()
		ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
		madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.BscMoney)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.BscMoney, madd)
		dal.Balance.Create(&entity.Balance{
			MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
			MerchantName: mch.Username,
			Trc:          mch.Money,
			Erc:          mch.ErcMoney,
			Bsc:          &madd,
			Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
		//商户余额变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(15)),
			Money:         &ying64,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Remark:        &str,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//商户手续费扣除
		//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
		//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
		//商户手续费变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(16)),
			Money:         &mchfee,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: order.Transactionid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//如果有代理 执行代理提成
		if *mch.AgentID != 0 {
			agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
			//demoney64 := decimal.NewFromFloat(emoney64)
			dagfee64 := decimal.NewFromFloat(*agent.Bscfee)
			//d1000 := decimal.NewFromInt(1000)
			//订单金额*代理费率/1000=代理提成
			agfeed := demoney64.Mul(dagfee64).Div(d1000)
			yingagadd, _ := mchfeed.Sub(agfeed).Float64()
			aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Bscmoney)).Float64()
			dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Bscmoney, aadd)
			//商户余额变动记录
			dal.AgentTran.Create(&entity.AgentTran{
				UserID:        lib.Int64ToInt32Ptr(agent.ID),
				Type:          lib.Int64ToInt32Ptr(int64(3)),
				Money:         &yingagadd,
				Orderid:       order.Orderid,
				Rorderid:      order.Rorderid,
				Transactionid: order.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": nil,
	})
}
