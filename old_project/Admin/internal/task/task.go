package task

import (
	"Admin/config"
	"Admin/dal"
	"Admin/internal/lib"
	"Admin/internal/transfer"
	"Admin/model/entity"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"mime/multipart"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type LeaseData struct {
	Duration  int     `json:"duration"`
	OrderNo   int64   `json:"orderNo"`
	State     int     `json:"state"`
	TrxAmount float64 `json:"trxAmount"`
	Value     int     `json:"value"`
}

type Lease struct {
	Data    LeaseData `json:"data"`
	Message string    `json:"message"`
	Status  int       `json:"status"`
}

type LeaseOrderData struct {
	Address      string  `json:"address"`
	FrozenTime   string  `json:"frozenTime"`
	Hash         string  `json:"hash"`
	State        int     `json:"state"`
	TrxAmount    float64 `json:"trxAmount"`
	UnfrozenTime string  `json:"unfrozenTime"`
	Value        int     `json:"value"`
}

type LeaseOrder struct {
	Data    LeaseOrderData `json:"data"`
	Message string         `json:"message"`
	Status  int            `json:"status"`
}

//func TrcCollection() {
//	printWithTime("执行Trc归集")
//	return
//	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ColAmount")).First()
//	merchant, _ := dal.Merchant.Where(dal.Merchant.GatherType.Eq(1)).Find()
//	int32Slice := make([]int32, len(merchant))
//	for i, l := range merchant {
//		int32Slice[i] = int32(l.ID)
//	}
//	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.In(int32Slice...)).Where(dal.Address.UsdtTrc.Gte(cast.ToFloat64(cfg.Value))).Where(dal.Address.Trx.Gt(0)).Find()
//	if len(address) == 0 || err != nil {
//		return
//	}
//	for _, v := range address {
//		//printWithTime(v.Trxaddr)
//		trx, trxe := transfer.QueryTron(*v.Trxaddr)
//		if trxe != nil {
//			continue
//		}
//		if trx == 0 {
//
//		}
//		//租能量
//		response, lerr := DoLease(*v.Trxaddr, "1", "65000", config.NewConfig().LeaseApi.Key)
//		if lerr != nil {
//			printWithTime("Error:", lerr)
//			return
//		}
//		if response.Status != 200 {
//			continue
//		}
//		orderno := cast.ToString(response.Data.OrderNo)
//		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Trxlock: lib.Int64ToInt32Ptr(1), Leaseid: &orderno})
//	}
//}

func QueryLeaseOrderToTransfer() {
	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Trxlock.Eq(1)).Find()
	if len(address) == 0 || err != nil {
		return
	}
	for _, v := range address {
		if *v.Leaseid == "1" {
			continue
		}

		_, err := CheckEnergyReceived(*v.Trxaddr)
		if err != "" {
			fmt.Println("能量未到账 1：")
			continue
		}

		//res, rerr := GetLeaseOrder(*v.Leaseid, config.NewConfig().LeaseApi.Key)
		//fmt.Println("QueryLeaseOrderToTransfer")
		//fmt.Println(res)
		//fmt.Println(rerr)
		//if rerr != nil {
		//	continue
		//}
		//if res.Status != 200 {
		//	continue
		//}
		//if res.Data.State != 1 {
		//	continue
		//}
		yi := "1"
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Trxlock: lib.Int64ToInt32Ptr(0), Leaseid: &yi})
		//能量到账 执行转账
		kk := lib.Dok(*v.Trxaddr, *v.Ethaddr, *v.Key)
		var txid string
		var state bool
		var terr error
		maxAttempts := 20
		for i := 0; i < maxAttempts; i++ {
			txid, state, terr = transfer.TransferUsdt(kk, *v.Trxaddr, config.NewConfig().Addr.Trc, *v.UsdtTrc)
			if terr != nil || state == false {
				printWithTime("向" + config.NewConfig().Addr.Trc + "转账USDT报错")
				printWithTime(terr)
			} else {
				break
			}
		}
		printWithTime("归集成功," + txid)
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Update(dal.Address.UpdateMoney, 1)
	}
}

func QueryLeaseOrderToMchTransfer() {
	lease, err := dal.Lease.Where(dal.Lease.Status.Eq(0)).Find()
	if len(lease) == 0 || err != nil {
		return
	}
	for _, v := range lease {
		addressID := int64(*v.Addressid)
		address, _ := dal.Address.Where(dal.Address.ID.Eq(addressID)).First()
		if *address.Leaseid != cast.ToString(v.ID) {
			dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Leaseid, v.ID)
		}
	}
	// address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Trxlock.Eq(0)).Where(dal.Address.Leaseid.Neq("1")).Find()
	// if len(address) == 0 || err != nil {
	// 	return
	// }
	// for _, v := range address {

	// 	_, err := CheckEnergyReceived(*v.Trxaddr)
	// 	if err != nil {
	// 		fmt.Println("能量未到账 2：" + err.Error())
	// 		continue
	// 	}

	// 	if *v.Leaseid == "1" {
	// 		continue
	// 	}
	// 	//res, rerr := GetLeaseOrder(*v.Leaseid, config.NewConfig().LeaseApi.Key)
	// 	//fmt.Println("QueryLeaseOrderToMchTransfer")
	// 	//fmt.Println(rerr)
	// 	//if rerr != nil {
	// 	//	continue
	// 	//}
	// 	//fmt.Println(res.Status)
	// 	//fmt.Println(res.Data.State)
	// 	//if res.Status != 200 {
	// 	//	continue
	// 	//}
	// 	//if res.Data.State != 1 {
	// 	//	continue
	// 	//}
	// 	yi := "1"
	// 	dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{TrxStatus: lib.Int64ToInt32Ptr(2), Leaseid: &yi})
	// }
}

func QueryLeaseOrderToPayOrder() {
	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Trxlock.Eq(0)).Where(dal.Address.Leaseid.Neq("1")).Find()
	if len(address) == 0 || err != nil {
		return
	}
	lease, err := dal.Lease.Where(dal.Lease.Status.Eq(0)).Find()
	if len(lease) == 0 || err != nil {
		return
	}
	for _, v := range address {
		_, err := CheckEnergyReceived(*v.Trxaddr)
		if err != "" {
			fmt.Println("能量未到账 2：")
			return
		}
	}

	for _, v := range lease {
		//res, rerr := GetLeaseOrder(*v.Leaseid, config.NewConfig().LeaseApi.Key)
		//if rerr != nil {
		//	continue
		//}
		//if res.Status != 200 {
		//	continue
		//}
		//if res.Data.State != 1 {
		//	continue
		//}
		dal.Lease.Where(dal.Lease.ID.Eq(v.ID)).Update(dal.Lease.Status, 1)
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.Addressid))).First()
		keycfg := *zongaddr.Key
		trccfg := *zongaddr.Trxaddr
		erccfg := *zongaddr.Ethaddr
		trcfeecfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRCFEE")).First()
		trcfee := cast.ToFloat64(trcfeecfg.Value)
		kk := lib.Dok(trccfg, erccfg, keycfg)
		order, _ := dal.Payorder.Where(dal.Payorder.ID.Eq(cast.ToInt64(v.Orderid))).First()
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(lib.StrToint64(*order.MerchantID))).First()
		m64, _ := strconv.ParseFloat(*order.Money, 64)
		//根据收款地址估算矿工费
		var transactionFeeInTrx float64
		var ferr error
		transactionFeeInTrx, ferr = transfer.GetTranFeeByAddress(*order.Addr)
		//波场
		var txid string
		var terr error
		var state bool
		if *order.Coin == "USDT" {
			txid, state, terr = transfer.TransferUsdt(kk, trccfg, *order.Addr, lib.StringToFloat64(*order.Money))
			printWithTime(state)
			//continue
		} else {
			txid, terr = transfer.TransferTron(kk, trccfg, *order.Addr, lib.StringToFloat64(*order.Money))
		}

		if terr != nil {
			printWithTime(terr)
			continue
		}
		printWithTime("向" + *order.Addr + "转账USDT成功,Hash:" + txid)
		if ferr != nil {
			transactionFeeInTrx = 14
		}
		//更新订单状态
		dal.Payorder.Where(dal.Payorder.ID.Eq(order.ID)).Updates(&entity.Payorder{
			Transactionid: &txid,
			TranFee:       &transactionFeeInTrx,
			Status:        lib.Int64ToInt32Ptr(2),
			Etime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//计费处理
		//商户余额扣除
		yingsub, _ := decimal.NewFromFloat(m64).Add(decimal.NewFromFloat(trcfee)).Float64()
		madd, _ := decimal.NewFromFloat(*mch.LockMoney).Sub(decimal.NewFromFloat(yingsub)).Float64()
		dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.LockMoney, madd)
		//商户余额变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(3)),
			Money:         &m64,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: &txid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		//暂定矿工费为商户下发手续费
		//商户手续费扣除
		//newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(transactionFeeInTrx)).Float64()
		//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
		//商户手续费变动记录
		dal.Tran.Create(&entity.Tran{
			UserID:        lib.Int64ToInt32Ptr(mch.ID),
			UserType:      lib.Int64ToInt32Ptr(int64(1)),
			Type:          lib.Int64ToInt32Ptr(int64(13)),
			Money:         &trcfee,
			Orderid:       order.Orderid,
			Rorderid:      order.Rorderid,
			Transactionid: &txid,
			Username:      mch.Username,
			Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
		})
		yi := "1"
		dal.Address.Where(dal.Address.ID.Eq(zongaddr.ID)).Update(dal.Address.Leaseid, yi)
	}
}

func DoLease(address string, duration string, value string, key string) (*Lease, error) {
	// 创建一个multipart/form-data的buffer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加form-data参数
	writer.WriteField("address", address)
	writer.WriteField("duration", duration)
	writer.WriteField("value", value)

	// 关闭writer
	err := writer.Close()
	if err != nil {
		return nil, err
	}

	// 创建请求
	tourl := config.NewConfig().LeaseApi.Url + "/api/v1/order"
	printWithTime(tourl)
	req, err := http.NewRequest("POST", tourl, body)
	if err != nil {
		return nil, err
	}

	// 添加header参数
	req.Header.Add("x-api-key", key)
	req.Header.Add("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	// 解析响应到结构体
	var response Lease
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func GetLeaseOrder(id string, key string) (*LeaseOrder, error) {
	// 创建URL
	tourl := config.NewConfig().LeaseApi.Url + "/api/v1/order/" + id

	// 创建请求
	req, err := http.NewRequest("GET", tourl, nil)
	if err != nil {
		return nil, err
	}

	// 添加header参数
	req.Header.Add("x-api-key", key)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	// 解析响应到结构体
	var response LeaseOrder
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func ActivationAddress() {
	address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.TrxStatus.Eq(0)).Find()
	if len(address) == 0 || aerr != nil {
		return
	}
	kk := lib.Dok(config.NewConfig().Addr.Trc, config.NewConfig().Addr.Erc, config.NewConfig().Addr.Key)
	for _, v := range address {
		txid, terr := transfer.TransferTron(kk, config.NewConfig().Addr.Trc, *v.Trxaddr, 0.1)
		if terr != nil {
			printWithTime(terr)
			printWithTime("激活地址失败")
			continue
		}
		printWithTime("激活地址成功," + txid)
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Update(dal.Address.TrxStatus, 1)
	}
}

func ErcCollection() {
	printWithTime("执行Erc归集")
	return
	cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ColAmount")).First()
	merchant, _ := dal.Merchant.Where(dal.Merchant.GatherType.Eq(1)).Find()
	int32Slice := make([]int32, len(merchant))
	for i, l := range merchant {
		int32Slice[i] = int32(l.ID)
	}
	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.In(int32Slice...)).Where(dal.Address.UsdtErc.Gte(cast.ToFloat64(cfg.Value))).Where(dal.Address.Ethlock.Eq(0)).Find()
	if len(address) == 0 || err != nil {
		return
	}
	trccfg := config.NewConfig().Addr.Trc
	erccfg := config.NewConfig().Addr.Erc
	keycfg := config.NewConfig().Addr.Key
	kk := lib.Dok(trccfg, erccfg, keycfg)
	for _, v := range address {
		if *v.Eth >= 0.008 {
			continue
		}
		cha, _ := decimal.NewFromFloat(0.008).Sub(decimal.NewFromFloat(*v.Eth)).Float64()
		hash, terr := transfer.EthSend(kk, *v.Ethaddr, cha)
		if terr != nil {
			printWithTime(*v.Ethaddr + " 归集转账ETH时报错")
			printWithTime(terr)
			continue
		}
		kong := ""
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Ethlock: lib.Int64ToInt32Ptr(1), Ethhash: &hash, Usdthash: &kong})
	}
}

func QueryEthDoErcCollection() {
	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.UsdtErc.Gte(1)).Where(dal.Address.Ethlock.Eq(1)).Where(dal.Address.Eth.Gte(0.008)).Find()
	if len(address) == 0 || err != nil {
		return
	}
	erccfg := config.NewConfig().Addr.Erc
	for _, v := range address {
		kk := lib.Dok(*v.Trxaddr, *v.Ethaddr, *v.Key)
		hash, _, terr := transfer.Erc20Send(kk, erccfg, *v.UsdtErc)
		if terr != nil {
			printWithTime(*v.Ethaddr + " 归集转账USDT时报错")
			printWithTime(terr)
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Ethlock: lib.Int64ToInt32Ptr(0), Usdthash: &hash, UpdateMoney: lib.Int64ToInt32Ptr(1)})
	}
}

func QueryBnbDoBscCollection() {
	address, err := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.UsdtBsc.Gte(1)).Where(dal.Address.Bnblock.Eq(1)).Where(dal.Address.Bnb.Gte(0.002)).Find()
	if len(address) == 0 || err != nil {
		return
	}
	erccfg := config.NewConfig().Addr.Erc
	for _, v := range address {
		kk := lib.Dok(*v.Trxaddr, *v.Ethaddr, *v.Key)
		hash, terr := transfer.Bep20Send(kk, erccfg, *v.UsdtBsc)
		if terr != nil {
			printWithTime(*v.Ethaddr + " 归集转账USDT时报错")
			printWithTime(terr)
			continue
		}
		dal.Address.Where(dal.Address.ID.Eq(v.ID)).Updates(&entity.Address{Bnblock: lib.Int64ToInt32Ptr(0), Usdthash: &hash, UpdateMoney: lib.Int64ToInt32Ptr(1)})
	}
}

func QueryGuiji() {
	settlement, err := dal.Settlement.Where(dal.Settlement.Status.Eq(0)).Find()
	if len(settlement) == 0 || err != nil {
		return
	}
	for _, v := range settlement {
		if *v.Chain == "Trc20" {
			usdt, uerr := transfer.QueryUSDT(*v.Address)
			if uerr != nil {
				continue
			}
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(mch.AddressID))).First()
			keycfg := *zongaddr.Key
			trccfg := *zongaddr.Trxaddr
			erccfg := *zongaddr.Ethaddr
			if keycfg == "" || trccfg == "" || erccfg == "" {
				continue
			}
			kk := lib.Dok(trccfg, erccfg, keycfg)
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 3)
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Trcsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtTrc.Gt(0)).Where(dal.Address.Num.Eq(1)).Where(dal.Address.TrxStatus.Eq(1)).Where(dal.Address.Leaseid.Eq("1")).Order(dal.Address.ID).Find()
			if len(address) == 0 || aerr != nil {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
				continue
			}
			num := 0
			fail := 0
			for _, a := range address {
				akk := lib.Dok(*a.Trxaddr, *a.Ethaddr, *a.Key)
				if usdt == 0 && num == 0 {
					trx, trxerr := transfer.QueryTron(*a.Trxaddr)
					if trxerr != nil {
						printWithTime(trxerr)
						txid, terr := transfer.TransferTron(kk, trccfg, *a.Trxaddr, 3)
						if terr != nil {
							printWithTime("向" + *a.Trxaddr + "转账TRX报错1")
							printWithTime(terr)
							fail = 1
							continue
						}
						printWithTime("向" + *a.Trxaddr + "转账TRX成功1,Hash:" + txid)
					}
					if trx < 1 {
						txid, terr := transfer.TransferTron(kk, trccfg, *a.Trxaddr, 3)
						if terr != nil {
							printWithTime("向" + *a.Trxaddr + "转账TRX报错1")
							printWithTime(terr)
							fail = 1
							continue
						}
						printWithTime("向" + *a.Trxaddr + "转账TRX成功1,Hash:" + txid)
					}
					Energy, _ := CheckEnergyReceived(*a.Trxaddr)
					if Energy < 100000 {
						responseTG, err := DoLeaseTG(*a.Trxaddr, akk)
						if err != nil {
							fmt.Println("给" + trccfg + "租能量报错，错误为1：" + err.Error())
							fail = 1
							continue
						}
						if responseTG {
							energy, err := CheckEnergyReceived(*a.Trxaddr)
							if err != "" {
								fmt.Println("检查能量失败1：")
								fail = 1
								continue
							}
							log.Printf("租能量成功55555")
							printWithTime("给" + *a.Trxaddr + "租能量成功1,订单号:" + cast.ToString(energy))
							num = num + 1
							// orderno := cast.ToString(energy)
							trxs := cast.ToInt32(2)
							dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{TrxStatus: &trxs})
							continue
						}
						continue
					} else {
						log.Printf("租能量成功,剩余 %v", Energy)
					}

					//租能量
					//response, lerr := DoLease(*a.Trxaddr, "1", "131000", config.NewConfig().LeaseApi.Key)
					//if lerr != nil {
					//	printWithTime("给" + *a.Trxaddr + "租能量报错")
					//	printWithTime("Error:" + lerr.Error())
					//	fail = 1
					//	continue
					//}
					//if response.Status != 200 {
					//	printWithTime("给" + *a.Trxaddr + "租能量状态码错误")
					//	printWithTime("Status:" + cast.ToString(response.Status))
					//	fail = 1
					//	continue
					//}
				}
				trx, trxerr := transfer.QueryTron(*a.Trxaddr)
				if trxerr != nil {
					printWithTime(trxerr)
					txid, terr := transfer.TransferTron(kk, trccfg, *a.Trxaddr, 3)
					if terr != nil {
						printWithTime("向" + *a.Trxaddr + "转账TRX报错2")
						printWithTime(terr)
						fail = 1
						continue
					}
					printWithTime("向" + *a.Trxaddr + "转账TRX成功2,Hash:" + txid)
				}
				if trx < 1 {
					txid, terr := transfer.TransferTron(kk, trccfg, *a.Trxaddr, 3)
					if terr != nil {
						printWithTime("向" + *a.Trxaddr + "转账TRX报错2")
						printWithTime(terr)
						fail = 1
						continue
					}
					printWithTime("向" + *a.Trxaddr + "转账TRX成功2,Hash:" + txid)
				}
				Energy, _ := CheckEnergyReceived(*a.Trxaddr)
				if Energy < 100000 {
					responseTG, err := DoLeaseTG(*a.Trxaddr, akk)
					if err != nil {
						fmt.Println("给" + trccfg + "租能量报错，错误为2：" + err.Error())
						fail = 1
						continue
					}
					if responseTG {
						energy, err := CheckEnergyReceived(*a.Trxaddr)
						if err != "" {
							fmt.Println("检查能量失败2：")
							fail = 1
							continue
						}
						log.Printf("租能量成功6666")
						printWithTime("给" + *a.Trxaddr + "租能量成功2,订单号:" + cast.ToString(energy))
						num = num + 1
						// orderno := cast.ToString(energy)
						trxs := cast.ToInt32(2)
						dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{TrxStatus: &trxs})
						continue
					}
					continue
				} else {
					log.Printf("租能量成功,剩余 %v", Energy)
				}

				//response, lerr := DoLease(*a.Trxaddr, "1", "65000", config.NewConfig().LeaseApi.Key)
				//if lerr != nil {
				//	printWithTime("给" + *a.Trxaddr + "租能量报错")
				//	printWithTime("Error:" + lerr.Error())
				//	fail = 1
				//	continue
				//}
				//if response.Status != 200 {
				//	printWithTime("给" + *a.Trxaddr + "租能量状态码错误")
				//	printWithTime("Status:" + cast.ToString(response.Status))
				//	fail = 1
				//	continue
				//}
				//printWithTime("给" + *a.Trxaddr + "租能量成功,订单号:" + cast.ToString(response.Data.OrderNo))
				//orderno := cast.ToString(response.Data.OrderNo)
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{Leaseid: &orderno})
				//num = num + 1
				time.Sleep(1 * time.Second)
			}
			if fail == 0 {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 1)
			} else {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
			}
		} else if *v.Chain == "Erc20" {
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(mch.AddressID))).First()
			keycfg := *zongaddr.Key
			trccfg := *zongaddr.Trxaddr
			erccfg := *zongaddr.Ethaddr
			//keycfg := config.NewConfig().Addr.Key
			//trccfg := config.NewConfig().Addr.Trc
			//erccfg := config.NewConfig().Addr.Erc
			if keycfg == "" || trccfg == "" || erccfg == "" {
				continue
			}
			kk := lib.Dok(trccfg, erccfg, keycfg)
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 3)
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Ercsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtErc.Gt(0)).Where(dal.Address.Ercnum.Eq(1)).Where(dal.Address.EthStatus.Eq(1)).Order(dal.Address.ID).Find()
			if len(address) == 0 || aerr != nil {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
				continue
			}
			fail := 0
			for _, a := range address {
				if *a.Eth >= 0.008 {
					dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.EthStatus, 2)
					continue
				}
				cha, _ := decimal.NewFromFloat(0.008).Sub(decimal.NewFromFloat(*a.Eth)).Float64()
				hash, terr := transfer.EthSend(kk, *a.Ethaddr, cha)
				if terr != nil {
					printWithTime(*a.Ethaddr + " 归集转账ETH时报错")
					printWithTime(terr)
					fail = 1
					continue
				}
				dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.EthStatus, 2)
				printWithTime("向" + *a.Ethaddr + "转账ETH成功,Hash:" + hash)
				//feemch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).First()
				//newfeemoney, _ := decimal.NewFromFloat(*feemch.EthFeemoney).Sub(decimal.NewFromFloat(cha)).Float64()
				//dal.Merchant.Where(dal.Merchant.ID.Eq(feemch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
				//str := "归集消耗商户手续费余额"
				//dal.Tran.Create(&entity.Tran{
				//	UserID:        lib.Int64ToInt32Ptr(mch.ID),
				//	UserType:      lib.Int64ToInt32Ptr(int64(1)),
				//	Type:          lib.Int64ToInt32Ptr(int64(14)),
				//	Money:         &cha,
				//	Orderid:       &str,
				//	Rorderid:      &str,
				//	Transactionid: &hash,
				//	Username:      mch.Username,
				//	Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				//})
				time.Sleep(1 * time.Second)
			}
			if fail == 0 {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 1)
			} else {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
			}
		} else {
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(mch.AddressID))).First()
			keycfg := *zongaddr.Key
			trccfg := *zongaddr.Trxaddr
			erccfg := *zongaddr.Ethaddr
			//keycfg := config.NewConfig().Addr.Key
			//trccfg := config.NewConfig().Addr.Trc
			//erccfg := config.NewConfig().Addr.Erc
			if keycfg == "" || trccfg == "" || erccfg == "" {
				continue
			}
			kk := lib.Dok(trccfg, erccfg, keycfg)
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 3)
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Bscsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtBsc.Gt(0)).Where(dal.Address.Bscnum.Eq(1)).Where(dal.Address.BscStatus.Eq(1)).Order(dal.Address.ID).Find()
			if len(address) == 0 || aerr != nil {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
				continue
			}
			fail := 0
			for _, a := range address {
				if *a.Bnb >= 0.002 {
					dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.BscStatus, 2)
					continue
				}
				cha, _ := decimal.NewFromFloat(0.002).Sub(decimal.NewFromFloat(*a.Bnb)).Float64()
				hash, terr := transfer.BnbSend(kk, *a.Ethaddr, cha)
				if terr != nil {
					printWithTime(*a.Ethaddr + " 归集转账BNB时报错")
					printWithTime(terr)
					fail = 1
					continue
				}
				dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.BscStatus, 2)
				printWithTime("向" + *a.Ethaddr + "转账BNB成功,Hash:" + hash)
				//feemch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).First()
				//newfeemoney, _ := decimal.NewFromFloat(*feemch.BscFeemoney).Sub(decimal.NewFromFloat(cha)).Float64()
				//dal.Merchant.Where(dal.Merchant.ID.Eq(feemch.ID)).Update(dal.Merchant.BscFeemoney, newfeemoney)
				//str := "归集消耗商户手续费余额"
				//dal.Tran.Create(&entity.Tran{
				//	UserID:        lib.Int64ToInt32Ptr(mch.ID),
				//	UserType:      lib.Int64ToInt32Ptr(int64(1)),
				//	Type:          lib.Int64ToInt32Ptr(int64(19)),
				//	Money:         &cha,
				//	Orderid:       &str,
				//	Rorderid:      &str,
				//	Transactionid: &hash,
				//	Username:      mch.Username,
				//	Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				//})
				time.Sleep(1 * time.Second)
			}
			if fail == 0 {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 1)
			} else {
				dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 0)
			}
		}
	}
}

func DoGuiji() {
	fmt.Println("DoGuiji")
	settlement, err := dal.Settlement.Where(dal.Settlement.Status.Eq(1)).Find()
	if len(settlement) == 0 || err != nil {
		return
	}
	//cfg, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TrcTranFee")).First()
	//arr := strings.Split(cfg.Value, ",")
	for _, v := range settlement {
		if *v.Chain == "Trc20" {
			//demoney := decimal.NewFromFloat32(*v.Money)
			//var zong decimal.Decimal
			//zong = decimal.NewFromFloat(0)
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Trcsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtTrc.Gt(0)).Where(dal.Address.TrxStatus.Eq(2)).Where(dal.Address.Num.Eq(1)).Find()
			fmt.Println(len(address))
			fmt.Println(aerr)
			if len(address) == 0 || aerr != nil {
				continue
			}
			addrc, _ := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Trcsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtTrc.Gt(0)).Where(dal.Address.Num.Eq(1)).Count()
			fmt.Println(addrc)
			if addrc != cast.ToInt64(len(address)) {
				continue
			}
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 2)
			time.Sleep(5 * time.Second)
			//num := decimal.NewFromFloat(0)
			for _, a := range address {
				//if demoney.Cmp(num) == -1 || demoney.Cmp(num) == 0 {
				//	break
				//}
				//var money float64
				//deusdt := decimal.NewFromFloat(*a.UsdtTrc)
				//sum := deusdt.Add(num)
				//switch demoney.Cmp(sum) {
				//case -1:
				//	money, _ = demoney.Sub(num).Float64()
				//	checknum = 1
				//case 0:
				//	money, _ = deusdt.Float64()
				//case 1:
				//	money, _ = deusdt.Float64()
				//}
				//transactionFeeInTrx, ferr := transfer.GetTranFeeByAddress(*v.Address)
				//if ferr != nil {
				//	transactionFeeInTrx = cast.ToFloat64(arr[0])
				//}
				kk := lib.Dok(*a.Trxaddr, *a.Ethaddr, *a.Key)
				var txid string
				var state bool
				var terr error
				maxAttempts := 20
				success := 0
				var transferusdt float64
				tusdt, tusdte := transfer.QueryUSDT(*a.Trxaddr)
				if tusdte != nil {
					transferusdt = *a.UsdtTrc
				} else {
					transferusdt = tusdt
				}
				if tusdte != nil {
					printWithTime(*a.Trxaddr + "获取USDT余额时报错:" + tusdte.Error())
					continue
				}
				for i := 0; i < maxAttempts; i++ {
					printWithTime(kk)
					txid, state, terr = transfer.TransferUsdt(kk, *a.Trxaddr, *v.Address, transferusdt)
					printWithTime("转账状态:")
					printWithTime(state)
					if terr != nil || state == false {
						printWithTime(*a.Trxaddr + "向" + *v.Address + "转账USDT报错")
						printWithTime(terr)
					} else {
						success = 1
						break
					}
					time.Sleep(time.Second * 1)
				}

				if success == 1 {
					printWithTime(*a.Trxaddr + "向" + *v.Address + "转账USDT成功,Hash:" + txid)
					//feemch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).First()
					//newfeemoney, _ := decimal.NewFromFloat(*feemch.FeeMoney).Sub(decimal.NewFromFloat(transactionFeeInTrx)).Float64()
					//dal.Merchant.Where(dal.Merchant.ID.Eq(feemch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
					//str := "归集消耗商户手续费余额"
					//dal.Tran.Create(&entity.Tran{
					//	UserID:        lib.Int64ToInt32Ptr(mch.ID),
					//	UserType:      lib.Int64ToInt32Ptr(int64(1)),
					//	Type:          lib.Int64ToInt32Ptr(int64(13)),
					//	Money:         &transactionFeeInTrx,
					//	Orderid:       &str,
					//	Rorderid:      &str,
					//	Transactionid: &txid,
					//	Username:      mch.Username,
					//	Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
					//})
					//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Num: lib.Int64ToInt32Ptr(0)})
					//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Trcsid, 0)
					//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.TrxStatus, 1)
					res, rerr := dal.Address.Where(dal.Address.ID.Eq(a.ID)).UpdateSimple(dal.Address.UpdateMoney.Value(1), dal.Address.Num.Value(0), dal.Address.Trcsid.Value(0), dal.Address.TrxStatus.Value(1))
					printWithTime(res)
					printWithTime(rerr)
					//num = num.Add(decimal.NewFromFloat(money))
					//zong = zong.Add(decimal.NewFromFloat(money))
				} else {
					strmoney := cast.ToString(transferusdt)
					chain := "TRC"
					dal.Uorder.Create(&entity.Uorder{
						Addrid: lib.Int64ToInt32Ptr(a.ID),
						Toaddr: v.Address,
						Money:  &strmoney,
						Chain:  &chain,
						Status: lib.Int64ToInt32Ptr(0),
						Ctime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
					})
					dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.TrxStatus, 1)
				}
				time.Sleep(1 * time.Second)
			}
		} else if *v.Chain == "Erc20" {
			client, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
			if cerr != nil {
				printWithTime(cerr)
				printWithTime("Eth网络连接失败")
				continue
			}
			demoney := decimal.NewFromFloat32(*v.Money)
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Ercsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtErc.Gt(0)).Where(dal.Address.EthStatus.Eq(2)).Where(dal.Address.Eth.Gte(0.0019)).Where(dal.Address.Ercnum.Eq(1)).Find()
			if len(address) == 0 || aerr != nil {
				continue
			}
			addrc, _ := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.Ercsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtErc.Gt(0)).Where(dal.Address.EthStatus.Eq(2)).Where(dal.Address.Ercnum.Eq(1)).Count()
			if addrc != cast.ToInt64(len(address)) {
				continue
			}
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 2)
			num := decimal.NewFromFloat(0)
			for _, a := range address {
				if demoney.Cmp(num) == -1 || demoney.Cmp(num) == 0 {
					break
				}
				//deusdt := decimal.NewFromFloat(*a.UsdtErc)
				//sum := deusdt.Add(num)
				//switch demoney.Cmp(sum) {
				//case -1:
				//	money, _ = demoney.Sub(num).Float64()
				//	checknum = 1
				//case 0:
				//	money, _ = deusdt.Float64()
				//case 1:
				//	money, _ = deusdt.Float64()
				//}
				kk := lib.Dok(*a.Trxaddr, *a.Ethaddr, *a.Key)
				//txid, terr := transfer.TransferUsdt(kk, *a.Trxaddr, *v.Address, money)
				var transferusdt float64
				eusdt, eusdte := transfer.ETHGetUSDT(client, *a.Ethaddr)
				if eusdte != nil {
					transferusdt = *a.UsdtErc
				} else {
					transferusdt = cast.ToFloat64(eusdt)
				}
				if eusdte != nil {
					printWithTime(*a.Ethaddr + "获取USDT余额时报错:" + eusdte.Error())
					continue
				}
				txid, _, terr := transfer.Erc20Send(kk, *v.Address, transferusdt)
				if terr != nil {
					printWithTime(*a.Ethaddr + "向" + *v.Address + "转账USDT报错")
					printWithTime(terr)
					strmoney := cast.ToString(transferusdt)
					chain := "ERC"
					dal.Uorder.Create(&entity.Uorder{
						Addrid: lib.Int64ToInt32Ptr(a.ID),
						Toaddr: v.Address,
						Money:  &strmoney,
						Chain:  &chain,
						Status: lib.Int64ToInt32Ptr(0),
						Ctime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
					})
					time.Sleep(1 * time.Second)
					dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.EthStatus, 1)
					continue
				}
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Ercnum: lib.Int64ToInt32Ptr(0)})
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Ercsid, 0)
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.EthStatus, 1)
				res, rerr := dal.Address.Where(dal.Address.ID.Eq(a.ID)).UpdateSimple(dal.Address.UpdateMoney.Value(1), dal.Address.Ercnum.Value(0), dal.Address.Ercsid.Value(0), dal.Address.EthStatus.Value(1))
				printWithTime(res)
				printWithTime(rerr)
				printWithTime(*a.Ethaddr + "向" + *v.Address + "转账USDT成功,Hash:" + txid)
				time.Sleep(1 * time.Second)
			}
			client.Close()
			//omoney, _ := decimal.NewFromFloat32(*v.Money).Float64()
			//madd, _ := decimal.NewFromFloat(*mch.ErcLockmoney).Sub(decimal.NewFromFloat32(*v.Money)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcLockmoney, madd)
			//str := "归集支出商户余额"
			//dal.Tran.Create(&entity.Tran{
			//	UserID:        lib.Int64ToInt32Ptr(mch.ID),
			//	UserType:      lib.Int64ToInt32Ptr(int64(1)),
			//	Type:          lib.Int64ToInt32Ptr(int64(8)),
			//	Money:         &omoney,
			//	Orderid:       &str,
			//	Rorderid:      &str,
			//	Transactionid: &str,
			//	Username:      mch.Username,
			//	Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			//})
		} else {
			client, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
			if cerr != nil {
				printWithTime(cerr)
				printWithTime("Bsc网络连接失败")
				continue
			}
			demoney := decimal.NewFromFloat32(*v.Money)
			mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(v.UserID))).First()
			address, aerr := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.UsdtBsc.Gt(0)).Where(dal.Address.Bscsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.BscStatus.Eq(2)).Where(dal.Address.Bnb.Gte(0.0019)).Where(dal.Address.Bscnum.Eq(1)).Find()
			if len(address) == 0 || aerr != nil {
				continue
			}
			addrc, _ := dal.Address.Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.UsdtBsc.Gt(0)).Where(dal.Address.Bscsid.Eq(cast.ToInt32(v.ID))).Where(dal.Address.BscStatus.Eq(2)).Where(dal.Address.Bscnum.Eq(1)).Count()
			if addrc != cast.ToInt64(len(address)) {
				continue
			}
			dal.Settlement.Where(dal.Settlement.ID.Eq(v.ID)).Update(dal.Settlement.Status, 2)
			num := decimal.NewFromFloat(0)
			for _, a := range address {
				if demoney.Cmp(num) == -1 || demoney.Cmp(num) == 0 {
					break
				}
				//deusdt := decimal.NewFromFloat(*a.UsdtErc)
				//sum := deusdt.Add(num)
				//switch demoney.Cmp(sum) {
				//case -1:
				//	money, _ = demoney.Sub(num).Float64()
				//	checknum = 1
				//case 0:
				//	money, _ = deusdt.Float64()
				//case 1:
				//	money, _ = deusdt.Float64()
				//}
				kk := lib.Dok(*a.Trxaddr, *a.Ethaddr, *a.Key)
				//txid, terr := transfer.TransferUsdt(kk, *a.Trxaddr, *v.Address, money)
				var transferusdt float64
				eusdt, eusdte := transfer.BNBGetUSDT(client, *a.Ethaddr)
				if eusdte != nil {
					transferusdt = *a.UsdtBsc
				} else {
					transferusdt = cast.ToFloat64(eusdt)
				}
				if eusdte != nil {
					printWithTime(*a.Ethaddr + "获取USDT余额时报错:" + eusdte.Error())
					continue
				}
				txid, terr := transfer.Bep20Send(kk, *v.Address, transferusdt)
				if terr != nil {
					printWithTime(*a.Ethaddr + "向" + *v.Address + "转账USDT报错")
					printWithTime(terr)
					strmoney := cast.ToString(transferusdt)
					chain := "BSC"
					dal.Uorder.Create(&entity.Uorder{
						Addrid: lib.Int64ToInt32Ptr(a.ID),
						Toaddr: v.Address,
						Money:  &strmoney,
						Chain:  &chain,
						Status: lib.Int64ToInt32Ptr(0),
						Ctime:  lib.Int64ToInt32Ptr(time.Now().Unix()),
					})
					time.Sleep(1 * time.Second)
					dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.BscStatus, 1)
					continue
				}
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Bscnum: lib.Int64ToInt32Ptr(0)})
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Bscsid, 0)
				//dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.BscStatus, 1)
				res, rerr := dal.Address.Where(dal.Address.ID.Eq(a.ID)).UpdateSimple(dal.Address.UpdateMoney.Value(1), dal.Address.Bscnum.Value(0), dal.Address.Bscsid.Value(0), dal.Address.BscStatus.Value(1))
				printWithTime(res)
				printWithTime(rerr)
				printWithTime(*a.Ethaddr + "向" + *v.Address + "转账USDT成功,Hash:" + txid)
				time.Sleep(1 * time.Second)
			}
			client.Close()
			//omoney, _ := decimal.NewFromFloat32(*v.Money).Float64()
			//madd, _ := decimal.NewFromFloat(*mch.ErcLockmoney).Sub(decimal.NewFromFloat32(*v.Money)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcLockmoney, madd)
			//str := "归集支出商户余额"
			//dal.Tran.Create(&entity.Tran{
			//	UserID:        lib.Int64ToInt32Ptr(mch.ID),
			//	UserType:      lib.Int64ToInt32Ptr(int64(1)),
			//	Type:          lib.Int64ToInt32Ptr(int64(8)),
			//	Money:         &omoney,
			//	Orderid:       &str,
			//	Rorderid:      &str,
			//	Transactionid: &str,
			//	Username:      mch.Username,
			//	Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			//})
		}
	}
}

func QueryUorder() {
	order, err := dal.Uorder.Where(dal.Uorder.Status.Eq(0)).Find()
	if len(order) == 0 || err != nil {
		return
	}
	for _, v := range order {
		if *v.Chain == "TRC" {
			address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.Addrid))).First()
			kk := lib.Dok(*address.Trxaddr, *address.Ethaddr, *address.Key)
			maxAttempts := 20
			for i := 0; i < maxAttempts; i++ {
				printWithTime(kk)
				txid, state, terr := transfer.TransferUsdt(kk, *address.Trxaddr, *v.Toaddr, cast.ToFloat64(v.Money))
				printWithTime("转账状态:")
				printWithTime(state)
				if terr != nil || state == false {
					printWithTime(*address.Trxaddr + "向" + *v.Toaddr + "转账USDT报错")
					printWithTime(terr)
				} else {
					printWithTime(*address.Trxaddr + "向" + *v.Toaddr + "转账USDT成功 :" + txid)
					dal.Uorder.Where(dal.Uorder.ID.Eq(v.ID)).Update(dal.Uorder.Status, 1)
					dal.Address.Where(dal.Address.ID.Eq(address.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Num: lib.Int64ToInt32Ptr(0)})
					break
				}
				time.Sleep(time.Second * 1)
			}
		} else if *v.Chain == "ERC" {
			address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.Addrid))).First()
			kk := lib.Dok(*address.Trxaddr, *address.Ethaddr, *address.Key)
			txid, _, terr := transfer.Erc20Send(kk, *v.Toaddr, cast.ToFloat64(v.Money))
			if terr != nil {
				printWithTime(*address.Ethaddr + "向" + *v.Toaddr + "转账USDT报错")
				printWithTime(terr)
			} else {
				printWithTime(*address.Ethaddr + "向" + *v.Toaddr + "转账USDT成功 :" + txid)
				dal.Uorder.Where(dal.Uorder.ID.Eq(v.ID)).Update(dal.Uorder.Status, 1)
				dal.Address.Where(dal.Address.ID.Eq(address.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Ercnum: lib.Int64ToInt32Ptr(0)})
			}
		} else {
			address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.Addrid))).First()
			kk := lib.Dok(*address.Trxaddr, *address.Ethaddr, *address.Key)
			txid, terr := transfer.Bep20Send(kk, *v.Toaddr, cast.ToFloat64(v.Money))
			if terr != nil {
				printWithTime(*address.Ethaddr + "向" + *v.Toaddr + "转账USDT报错")
				printWithTime(terr)
			} else {
				printWithTime(*address.Ethaddr + "向" + *v.Toaddr + "转账USDT成功 :" + txid)
				dal.Uorder.Where(dal.Uorder.ID.Eq(v.ID)).Update(dal.Uorder.Status, 1)
				dal.Address.Where(dal.Address.ID.Eq(address.ID)).Updates(&entity.Address{UpdateMoney: lib.Int64ToInt32Ptr(1), Ercnum: lib.Int64ToInt32Ptr(0)})
			}
		}
	}
}

func QueryBuDan() {
	order, err := dal.Order.Where(dal.Order.Appkey.Eq("1")).Find()
	if len(order) == 0 || err != nil {
		return
	}
	for _, v := range order {
		m := *v.Money
		mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(*v.MerchantID))).First()
		emoney64, _ := strconv.ParseFloat(m, 64)
		address, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.Addrid))).First()
		//解锁地址
		dal.Lockaddress.Where(dal.Lockaddress.Addrid.Eq(int32(address.ID))).Where(dal.Lockaddress.Money.Eq(*v.Money)).Update(dal.Lockaddress.Islock, 1)
		dal.Address.Where(dal.Address.ID.Eq(address.ID)).Update(dal.Address.Lock, 0)
		if *v.Chain == "Trc20" {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Fee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.Money)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.Money, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          &madd,
				Erc:          mch.ErcMoney,
				Bsc:          mch.BscMoney,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(1)),
				Money:         &ying64,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.FeeMoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.FeeMoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(2)),
				Money:         &mchfee,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Fee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Money)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Money, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(1)),
					Money:         &yingagadd,
					Orderid:       v.Orderid,
					Rorderid:      v.Rorderid,
					Transactionid: v.Transactionid,
					Username:      agent.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		} else if *v.Chain == "Erc20" {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Ethfee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.ErcMoney)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.ErcMoney, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          mch.Money,
				Erc:          &madd,
				Bsc:          mch.BscMoney,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(5)),
				Money:         &ying64,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(6)),
				Money:         &mchfee,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Ethfee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Ercmoney)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Ercmoney, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(2)),
					Money:         &yingagadd,
					Orderid:       v.Orderid,
					Rorderid:      v.Rorderid,
					Transactionid: v.Transactionid,
					Username:      agent.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		} else {
			//商户余额上分
			m64, _ := strconv.ParseFloat(m, 64)
			//商户手续费处理
			demoney64 := decimal.NewFromFloat(emoney64)
			dmchfee64 := decimal.NewFromFloat(*mch.Bscfee)
			d1000 := decimal.NewFromInt(1000)
			//订单金额*商户费率/1000=商户手续费
			mchfeed := demoney64.Mul(dmchfee64).Div(d1000)
			mchfee, _ := mchfeed.RoundCeil(6).Float64()
			ying64, _ := decimal.NewFromFloat(m64).Sub(decimal.NewFromFloat(mchfee)).Float64()
			madd, _ := decimal.NewFromFloat(ying64).Add(decimal.NewFromFloat(*mch.BscMoney)).Float64()
			dal.Merchant.Where(dal.Merchant.ID.Eq(int64(mch.ID))).Update(dal.Merchant.BscMoney, madd)
			dal.Balance.Create(&entity.Balance{
				MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
				MerchantName: mch.Username,
				Trc:          mch.Money,
				Erc:          mch.ErcMoney,
				Bsc:          &madd,
				Ctime:        lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			str := "收入 " + cast.ToString(m64) + " 手续费 " + cast.ToString(mchfee)
			//商户余额变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(15)),
				Money:         &ying64,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Remark:        &str,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//商户手续费扣除
			//newfeemoney, _ := decimal.NewFromFloat(*mch.EthFeemoney).Sub(decimal.NewFromFloat(mchfee)).Float64()
			//dal.Merchant.Where(dal.Merchant.ID.Eq(mch.ID)).Update(dal.Merchant.EthFeemoney, newfeemoney)
			//商户手续费变动记录
			dal.Tran.Create(&entity.Tran{
				UserID:        lib.Int64ToInt32Ptr(mch.ID),
				UserType:      lib.Int64ToInt32Ptr(int64(1)),
				Type:          lib.Int64ToInt32Ptr(int64(16)),
				Money:         &mchfee,
				Orderid:       v.Orderid,
				Rorderid:      v.Rorderid,
				Transactionid: v.Transactionid,
				Username:      mch.Username,
				Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
			})
			//如果有代理 执行代理提成
			if *mch.AgentID != 0 {
				agent, _ := dal.Agent.Where(dal.Agent.ID.Eq(cast.ToInt64(mch.AgentID))).First()
				//demoney64 := decimal.NewFromFloat(emoney64)
				dagfee64 := decimal.NewFromFloat(*agent.Bscfee)
				//d1000 := decimal.NewFromInt(1000)
				//订单金额*代理费率/1000=代理提成
				agfeed := demoney64.Mul(dagfee64).Div(d1000)
				yingagadd, _ := mchfeed.Sub(agfeed).Float64()
				aadd, _ := decimal.NewFromFloat(yingagadd).Add(decimal.NewFromFloat(*agent.Bscmoney)).Float64()
				dal.Agent.Where(dal.Agent.ID.Eq(int64(agent.ID))).Update(dal.Agent.Bscmoney, aadd)
				//商户余额变动记录
				dal.AgentTran.Create(&entity.AgentTran{
					UserID:        lib.Int64ToInt32Ptr(agent.ID),
					Type:          lib.Int64ToInt32Ptr(int64(3)),
					Money:         &yingagadd,
					Orderid:       v.Orderid,
					Rorderid:      v.Rorderid,
					Transactionid: v.Transactionid,
					Username:      agent.Username,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}

		dal.Order.Where(dal.Order.ID.Eq(v.ID)).Update(dal.Order.Appkey, mch.Appkey)
		dal.Order.Where(dal.Order.ID.Eq(v.ID)).Update(dal.Order.Etime, time.Now().Unix())
	}
}

func QueryMchTrcStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.TrcStatus.Eq(1)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		addr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtTrc.Gt(0)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Num.Eq(0)).Where(dal.Address.Leaseid.Eq("1")).Where(dal.Address.TrxStatus.Eq(1)).Find()
		if len(addr) == 0 {
			continue
		}
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		usdt, uerr := transfer.QueryUSDT(*zongaddr.Trxaddr)
		if uerr != nil {
			continue
		}
		var money decimal.Decimal
		for _, a := range addr {
			var transferusdt float64
			tusdt, tusdte := transfer.QueryUSDT(*a.Trxaddr)
			if tusdte != nil {
				printWithTime(tusdte)
				transferusdt = *a.UsdtTrc
			} else {
				transferusdt = tusdt
			}
			dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Num, 1)
			money = money.Add(decimal.NewFromFloat(transferusdt))
		}
		newmoney, _ := money.Float64()
		floatmoney := cast.ToFloat32(newmoney)
		needmoney := decimal.NewFromFloat(usdt).Add(money).String()
		chain := "Trc20"
		dal.Settlement.Create(&entity.Settlement{
			Type:      lib.Int64ToInt32Ptr(1),
			Money:     &floatmoney,
			UserID:    lib.Int64ToInt32Ptr(v.ID),
			Status:    lib.Int64ToInt32Ptr(0),
			Username:  v.Username,
			Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
			Address:   zongaddr.Trxaddr,
			Needmoney: &needmoney,
			Chain:     &chain,
		})
		set, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Chain.Eq(chain)).Where(dal.Settlement.Status.Eq(0)).First()
		for _, v := range addr {
			dal.Address.Where(dal.Address.ID.Eq(v.ID)).Update(dal.Address.Trcsid, set.ID)
		}
		dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Updates(&entity.Merchant{TrcStatus: lib.Int64ToInt32Ptr(2), Trcneedmoney: &needmoney})
	}
}

func QueryMchErcStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.ErcStatus.Eq(1)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		addr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtErc.Gt(0)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Ercnum.Eq(0)).Where(dal.Address.EthStatus.Eq(1)).Find()
		if len(addr) == 0 {
			continue
		}
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		ercclient, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
		if cerr != nil {
			continue
		}
		usdt, uerr := transfer.ETHGetUSDT(ercclient, *zongaddr.Ethaddr)
		if uerr != nil {
			continue
		}
		ercclient.Close()
		var money decimal.Decimal
		for _, a := range addr {
			dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Ercnum, 1)
			eusdt, eerr := transfer.ETHGetUSDT(ercclient, *a.Ethaddr)
			var tansferusdt float64
			if eerr != nil {
				tansferusdt = *a.UsdtErc
			} else {
				tansferusdt = cast.ToFloat64(eusdt)
			}
			money = money.Add(decimal.NewFromFloat(tansferusdt))
		}
		newmoney, _ := money.Float64()
		floatmoney := cast.ToFloat32(newmoney)
		needmoney := decimal.NewFromFloat(cast.ToFloat64(usdt)).Add(money).String()
		chain := "Erc20"
		dal.Settlement.Create(&entity.Settlement{
			Type:      lib.Int64ToInt32Ptr(1),
			Money:     &floatmoney,
			UserID:    lib.Int64ToInt32Ptr(v.ID),
			Status:    lib.Int64ToInt32Ptr(0),
			Username:  v.Username,
			Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
			Address:   zongaddr.Ethaddr,
			Needmoney: &needmoney,
			Chain:     &chain,
		})
		set, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Chain.Eq(chain)).Where(dal.Settlement.Status.Eq(0)).First()
		for _, v := range addr {
			dal.Address.Where(dal.Address.ID.Eq(v.ID)).Update(dal.Address.Ercsid, set.ID)
		}
		dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Updates(&entity.Merchant{ErcStatus: lib.Int64ToInt32Ptr(2), Ercneedmoney: &needmoney})
	}
}

func QueryMchBscStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.BscStatus.Eq(1)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		addr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.UsdtBsc.Gt(0)).Where(dal.Address.DelFlg.Eq(0)).Where(dal.Address.Bscnum.Eq(0)).Where(dal.Address.BscStatus.Eq(1)).Find()
		if len(addr) == 0 {
			continue
		}
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		ercclient, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
		if cerr != nil {
			continue
		}
		usdt, uerr := transfer.BNBGetUSDT(ercclient, *zongaddr.Ethaddr)
		if uerr != nil {
			continue
		}
		ercclient.Close()
		var money decimal.Decimal
		for _, a := range addr {
			dal.Address.Where(dal.Address.ID.Eq(a.ID)).Update(dal.Address.Bscnum, 1)
			eusdt, eerr := transfer.BNBGetUSDT(ercclient, *a.Ethaddr)
			var tansferusdt float64
			if eerr != nil {
				tansferusdt = *a.UsdtBsc
			} else {
				tansferusdt = cast.ToFloat64(eusdt)
			}
			money = money.Add(decimal.NewFromFloat(tansferusdt))
		}
		newmoney, _ := money.Float64()
		floatmoney := cast.ToFloat32(newmoney)
		needmoney := decimal.NewFromFloat(cast.ToFloat64(usdt)).Add(money).String()
		chain := "Bep20"
		dal.Settlement.Create(&entity.Settlement{
			Type:      lib.Int64ToInt32Ptr(1),
			Money:     &floatmoney,
			UserID:    lib.Int64ToInt32Ptr(v.ID),
			Status:    lib.Int64ToInt32Ptr(0),
			Username:  v.Username,
			Ctime:     lib.Int64ToInt32Ptr(time.Now().Unix()),
			Address:   zongaddr.Ethaddr,
			Needmoney: &needmoney,
			Chain:     &chain,
		})
		set, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Chain.Eq(chain)).Where(dal.Settlement.Status.Eq(0)).First()
		for _, v := range addr {
			dal.Address.Where(dal.Address.ID.Eq(v.ID)).Update(dal.Address.Bscsid, set.ID)
		}
		dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Updates(&entity.Merchant{BscStatus: lib.Int64ToInt32Ptr(2), Bscneedmoney: &needmoney})
	}
}

func UpdateMchTrcStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.TrcStatus.Eq(2)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		usdt, uerr := transfer.QueryUSDT(*zongaddr.Trxaddr)
		if uerr != nil {
			continue
		}
		needmoney := Float64ToIntToFloat64(cast.ToFloat64(*v.Trcneedmoney))
		if usdt >= needmoney {
			dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.TrcStatus, 0)
		}
	}
}

func UpdateMchErcStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.ErcStatus.Eq(2)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		ercclient, cerr := ethclient.Dial(config.NewConfig().Eth.Api)
		if cerr != nil {
			continue
		}
		usdt, uerr := transfer.ETHGetUSDT(ercclient, *zongaddr.Ethaddr)
		if uerr != nil {
			continue
		}
		ercclient.Close()
		floatusdt := cast.ToFloat64(usdt)
		needmoney := Float64ToIntToFloat64(cast.ToFloat64(*v.Ercneedmoney))
		if floatusdt >= needmoney {
			dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.ErcStatus, 0)
		}
	}
}

func UpdateMchBscStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.BscStatus.Eq(2)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		zongaddr, _ := dal.Address.Where(dal.Address.ID.Eq(cast.ToInt64(v.AddressID))).First()
		ercclient, cerr := ethclient.Dial(config.NewConfig().Bsc.Api)
		if cerr != nil {
			continue
		}
		usdt, uerr := transfer.BNBGetUSDT(ercclient, *zongaddr.Ethaddr)
		if uerr != nil {
			continue
		}
		ercclient.Close()
		floatusdt := cast.ToFloat64(usdt)
		needmoney := Float64ToIntToFloat64(cast.ToFloat64(*v.Bscneedmoney))
		if floatusdt >= needmoney {
			dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.BscStatus, 0)
		}
	}
}

// 根据跑量变更费率
func DoTieredRate() {
	printWithTime("执行根据跑量变更费率")
	mch, err := dal.Merchant.Where(dal.Merchant.IsTieredRate.Eq(1)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	now := time.Now()
	startOfYesterday := time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location()).Unix()
	endOfYesterday := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, -1, now.Location()).Unix()
	for _, v := range mch {
		daysql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'daymoney' from `order` where ctime>" + cast.ToString(startOfYesterday) + " and ctime<" + cast.ToString(endOfYesterday) + " and merchant_id = " + cast.ToString(v.ID) + " and status in (2,3)"
		dayresult := make(map[string]interface{})
		dal.DB.Raw(daysql).Scan(&dayresult)
		DaySuccessMoney := dayresult["daymoney"]
		money := cast.ToFloat64(DaySuccessMoney)
		oldfee := *v.Fee
		var newfee float64
		if money < 50000 {
			//1.1%
			newfee = float64(11)
		} else if money >= 50000 && money < 500000 {
			//1%
			newfee = float64(10)
		} else if money >= 500000 && money < 1000000 {
			//0.9%
			newfee = float64(9)
		} else if money >= 1000000 {
			//0.8%
			newfee = float64(8)
		}
		//dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Updates(&entity.Merchant{Fee: &newfee, Ethfee: &newfee, Bscfee: &newfee})
		if oldfee > newfee {
			cha, _ := decimal.NewFromFloat(oldfee).Sub(decimal.NewFromFloat(newfee)).Float64()
			decha := decimal.NewFromFloat(cha)
			d1000 := decimal.NewFromInt(1000)
			trcsql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'trcmoney' from `order` where ctime>" + cast.ToString(startOfYesterday) + " and ctime<" + cast.ToString(endOfYesterday) + " and merchant_id = " + cast.ToString(v.ID) + " and status in (2,3) and chain = 'Trc20'"
			trcresult := make(map[string]interface{})
			dal.DB.Raw(trcsql).Scan(&trcresult)
			trcmoney := cast.ToFloat64(trcresult["trcmoney"])
			if trcmoney > 0 {
				detrcmoney64 := decimal.NewFromFloat(trcmoney)
				ying := detrcmoney64.Mul(decha).Div(d1000)
				ying64, _ := ying.Float64()
				madd, _ := ying.Add(decimal.NewFromFloat(*v.Money)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.Money, madd)
				str := "跑量 " + cast.ToString(trcmoney) + " 应补比例千分之 " + cast.ToString(cha)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(v.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(22)),
					Money:         &ying64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      v.Username,
					Remark:        &str,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
			ercsql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'ercmoney' from `order` where ctime>" + cast.ToString(startOfYesterday) + " and ctime<" + cast.ToString(endOfYesterday) + " and merchant_id = " + cast.ToString(v.ID) + " and status in (2,3) and chain = 'Erc20'"
			ercresult := make(map[string]interface{})
			dal.DB.Raw(ercsql).Scan(&ercresult)
			ercmoney := cast.ToFloat64(ercresult["ercmoney"])
			if ercmoney > 0 {
				deercmoney64 := decimal.NewFromFloat(ercmoney)
				ying := deercmoney64.Mul(decha).Div(d1000)
				ying64, _ := ying.Float64()
				madd, _ := ying.Add(decimal.NewFromFloat(*v.ErcMoney)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.ErcMoney, madd)
				str := "跑量 " + cast.ToString(ercmoney) + " 应补比例千分之 " + cast.ToString(cha)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(v.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(23)),
					Money:         &ying64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      v.Username,
					Remark:        &str,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
			bscsql := "select SUM(CAST(money AS DECIMAL(30,2)))as 'bscmoney' from `order` where ctime>" + cast.ToString(startOfYesterday) + " and ctime<" + cast.ToString(endOfYesterday) + " and merchant_id = " + cast.ToString(v.ID) + " and status in (2,3) and chain = 'Bep20'"
			bscresult := make(map[string]interface{})
			dal.DB.Raw(bscsql).Scan(&bscresult)
			bscmoney := cast.ToFloat64(bscresult["bscmoney"])
			if bscmoney > 0 {
				debscmoney64 := decimal.NewFromFloat(bscmoney)
				ying := debscmoney64.Mul(decha).Div(d1000)
				ying64, _ := ying.Float64()
				madd, _ := ying.Add(decimal.NewFromFloat(*v.BscMoney)).Float64()
				dal.Merchant.Where(dal.Merchant.ID.Eq(v.ID)).Update(dal.Merchant.BscMoney, madd)
				str := "跑量 " + cast.ToString(bscmoney) + " 应补比例千分之 " + cast.ToString(cha)
				//商户余额变动记录
				dal.Tran.Create(&entity.Tran{
					UserID:        lib.Int64ToInt32Ptr(v.ID),
					UserType:      lib.Int64ToInt32Ptr(int64(1)),
					Type:          lib.Int64ToInt32Ptr(int64(24)),
					Money:         &ying64,
					Orderid:       &str,
					Rorderid:      &str,
					Transactionid: &str,
					Username:      v.Username,
					Remark:        &str,
					Ctime:         lib.Int64ToInt32Ptr(time.Now().Unix()),
				})
			}
		}
	}
}

func CheckAddressStatus() {
	mch, err := dal.Merchant.Where(dal.Merchant.GatherType.Eq(2)).Find()
	if len(mch) == 0 || err != nil {
		return
	}
	for _, v := range mch {
		checktrc, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Status.Neq(2)).Where(dal.Settlement.Chain.Eq("Trc20")).Find()
		if len(checktrc) == 0 {
			trcaddr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.TrxStatus.Eq(2)).Find()
			for _, t := range trcaddr {
				dal.Address.Where(dal.Address.ID.Eq(t.ID)).Update(dal.Address.TrxStatus, 1)
			}
		}
		checkerc, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Status.Neq(2)).Where(dal.Settlement.Chain.Eq("Erc20")).Find()
		if len(checkerc) == 0 {
			ercaddr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.EthStatus.Eq(2)).Find()
			for _, e := range ercaddr {
				dal.Address.Where(dal.Address.ID.Eq(e.ID)).Update(dal.Address.EthStatus, 1)
			}
		}
		checkbsc, _ := dal.Settlement.Where(dal.Settlement.UserID.Eq(cast.ToInt32(v.ID))).Where(dal.Settlement.Status.Neq(2)).Where(dal.Settlement.Chain.Eq("Bep20")).Find()
		if len(checkbsc) == 0 {
			bscaddr, _ := dal.Address.Where(dal.Address.MerchantID.Eq(cast.ToInt32(v.ID))).Where(dal.Address.BscStatus.Eq(2)).Find()
			for _, b := range bscaddr {
				dal.Address.Where(dal.Address.ID.Eq(b.ID)).Update(dal.Address.BscStatus, 1)
			}
		}
	}
}

func Float64ToIntToFloat64(f float64) float64 {
	f = float64(int(f))
	return f
}

func printWithTime(a ...interface{}) {
	fmt.Print(time.Now().Format("2006-01-02 15:04:05"))
	fmt.Print(" ")
	fmt.Println(a...)
}

func Gopool(run func(), i int64) {
	go func(n int64) {
		for {
			time.Sleep(time.Second * time.Duration(n))
			run()
		}
	}(i)
}

// DoLeaseTG 从tg 机器人获取能量 发送 trx  给返回对应的能量
func DoLeaseTG(address string, key string) (bool, error) {
	trxBalance, err := transfer.QueryTron(address)
	if err != nil {
		log.Printf("查询trx 失败 %v", err)
		return false, err
	}
	if trxBalance < 5 {
		_, err = transfer.TransferTron(key, address, config.NewConfig().TgTrxAddress.Address, config.NewConfig().TgTrxAddress.Num)
		if err != nil {
			log.Printf("发送trx 失败 %v", err)
			return false, err
		}
	}
	log.Printf("查询trx 余额 %v", trxBalance)
	//_, err = transfer.TransferTron(key, address, config.NewConfig().TgTrxAddress.Address, config.NewConfig().TgTrxAddress.Num)
	//if err != nil {
	//	return false, err
	//}
	return true, nil
}

// CheckEnergyReceived 查询能量到账情况
func CheckEnergyReceived(address string) (int64, string) {
	url := config.NewConfig().Tron.Api + "/wallet/getaccountresource"
	apiKey := config.NewConfig().Tron.AppKey
	payload := strings.NewReader("{\"address\":\" " + address + "\",\"visible\":true}")
	req, _ := http.NewRequest("POST", url, payload)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("TRON-PRO-API-KEY", apiKey)

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	body, _ := io.ReadAll(res.Body)

	type AccountInfo struct {
		EnergyLimit   int64 `json:"EnergyLimit"`
		TotalNetLimit int64 `json:"TotalNetLimit"`
	}

	var accountInfo AccountInfo
	if err := json.Unmarshal(body, &accountInfo); err != nil {
		log.Printf("解析JSON数据失败: %v", err)
	}
	log.Printf("EnergyLimit: %d, TotalNetLimit: %d", accountInfo.EnergyLimit, accountInfo.TotalNetLimit)
	energy := accountInfo.EnergyLimit
	if energy < 50000 {
		return energy, "未查到能量"
	}

	return energy, ""
}

func Pool() {
	Gopool(QueryLeaseOrderToTransfer, 5)
	Gopool(QueryLeaseOrderToMchTransfer, 5)
	Gopool(QueryLeaseOrderToPayOrder, 5)
	Gopool(QueryEthDoErcCollection, 60)
	//Gopool(QueryBnbDoBscCollection, 60)
	//Gopool(ActivationAddress, 600)
	//Gopool(UpdateTrcMoney, 30)
	//Gopool(UpdateErcMoney, 30)

	Gopool(QueryGuiji, 20)
	Gopool(DoGuiji, 20)
	Gopool(QueryBuDan, 10)
	Gopool(QueryMchTrcStatus, 5)
	Gopool(QueryMchErcStatus, 5)
	//Gopool(QueryMchBscStatus, 5)
	Gopool(UpdateMchTrcStatus, 10)
	Gopool(UpdateMchErcStatus, 10)
	//Gopool(UpdateMchBscStatus, 10)
	Gopool(QueryUorder, 30)
	Gopool(CheckAddressStatus, 60)
}
