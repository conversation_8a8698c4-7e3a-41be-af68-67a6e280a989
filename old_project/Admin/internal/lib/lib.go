package lib

import (
	"Admin/dal"
	"Admin/model/entity"
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"io"
	"io/ioutil"
	"math"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"
)

var (
	Iplist   = []string{}
	Rxf      = 0.00
	usdtRate = 7.0
	OkExApi  = "https://www.okx.com/v3/c2c/otc-ticker/quotedPrice?t={time}&side=buy&quoteCurrency=CNY&baseCurrency=USDT"
)

func TgSend(id, data, TgToken string) bool {
	Api := "https://api.telegram.org/bot" + TgToken + "/sendMessage"
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	type TgPost struct {
		Chat_id string `json:"chat_id"`
		Text    string `json:"text"`
	}
	var post TgPost
	post.Chat_id = id
	post.Text = data
	b, err := json.Marshal(post)
	if err != nil {
		return false
	}
	//post := `{"chat_id":` + id + `,"text":"` + data + `"}`
	reqest, err := http.NewRequest("POST", Api, bytes.NewBuffer(b))
	if err != nil {
		return false
	}
	reqest.Header.Set("Content-Type", "application/json")
	response, err := client.Do(reqest)
	if err != nil {
		return false
	}
	defer response.Body.Close()
	return true
}

func UsdtInt64ToFloat64(num int64) float64 {
	var SUN int64 = 1000000
	return float64(num) / float64(SUN)
}
func StrToint64(str string) int64 {
	i, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}
	return i
}
func GetCurrentAbPathByExecutable() string {
	exePath, err := os.Executable()
	if err != nil {
		return ""
	}
	res, _ := filepath.EvalSymlinks(filepath.Dir(exePath))
	return res
}
func ReadFile(filePath string) (string, error) {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), err
}
func StringToFloat64(str string) float64 {
	num, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0.00
	}
	return num
}
func Shuffle(slice []string) {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	time.Sleep(time.Microsecond * 1)
	for len(slice) > 0 {
		n := len(slice)
		randIndex := r.Intn(n)
		slice[n-1], slice[randIndex] = slice[randIndex], slice[n-1]
		slice = slice[:n-1]
	}
}
func CallbackGet(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Telegram USDT Server 1.0")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}
func Get(url string) string {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("User-Agent", "Mozilla/5.0 (Linux; U; Android 4.0.3; zh-cn; U8860 Build/HuaweiU8860) UC AppleWebKit/530+ (KHTML, like Gecko) Mobile Safari/530 ")
	//处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}

func Int64ToInt32Ptr(value int64) *int32 {
	if value < math.MinInt32 || value > math.MaxInt32 {
		return nil
	}
	result := int32(value)
	return &result
}

// 类型转换
func TypeConversion(value string, ntype string) (reflect.Value, error) {
	if ntype == "string" {
		return reflect.ValueOf(value), nil
	} else if ntype == "time.Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "Time" {
		t, err := time.ParseInLocation("2006-01-02 15:04:05", value, time.Local)
		return reflect.ValueOf(t), err
	} else if ntype == "int" {
		i, err := strconv.Atoi(value)
		return reflect.ValueOf(i), err
	} else if ntype == "int8" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int8(i)), err
	} else if ntype == "int32" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(int64(i)), err
	} else if ntype == "int64" {
		i, err := strconv.ParseInt(value, 10, 64)
		return reflect.ValueOf(i), err
	} else if ntype == "float32" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(float32(i)), err
	} else if ntype == "float64" {
		i, err := strconv.ParseFloat(value, 64)
		return reflect.ValueOf(i), err
	}

	//else if .......增加其他一些类型的转换
	return reflect.ValueOf(value), errors.New("未知的类型：" + ntype)
}
func Md5(s string) string {
	srcCode := md5.Sum([]byte(s))
	code := fmt.Sprintf("%x", srcCode)
	return string(code)
}
func FilteredSQLInject(to_match_str string) bool {
	//过滤 ‘
	//ORACLE 注解 --  /**/
	//关键字过滤 update ,delete
	// 正则的字符串, 不能用 " " 因为" "里面的内容会转义
	str := `(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\b(select|update|and|or|delete|insert|trancate|char|chr|into|substr|ascii|declare|exec|count|master|into|drop|execute)\b)`
	re, err := regexp.Compile(str)
	if err != nil {
		//panic(err.Error())
		//log.Println("注入")
		return false
	}
	//log.Println("没注入")
	return re.MatchString(to_match_str)
}
func RandAllString(lenNum int) string {
	var CHARS = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
		"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
		"1", "2", "3", "4", "5", "6", "7", "8", "9", "0"}
	str := strings.Builder{}
	length := len(CHARS)
	rand.Seed(time.Now().UnixNano())
	time.Sleep(time.Microsecond / 1000)
	for i := 0; i < lenNum; i++ {
		l := CHARS[rand.Intn(length)]
		str.WriteString(l)
	}
	return str.String()
}

func Decimal(num float64) float64 {
	num, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", num), 64)
	return num
}

type OkexJson struct {
	Code int `json:"code"`
	Data []struct {
		BestOption bool   `json:"bestOption"`
		Payment    string `json:"payment"`
		Price      string `json:"price"`
	} `json:"data"`
	DetailMsg    string `json:"detailMsg"`
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Msg          string `json:"msg"`
}

func GetExchange() float64 {
	api := strings.ReplaceAll(OkExApi, "{time}", fmt.Sprint(time.Now().UnixNano()/1e6))
	jsonstr := Get(api)
	if jsonstr == "" {
		return usdtRate
	}
	var jsondata OkexJson
	err := json.Unmarshal([]byte(jsonstr), &jsondata)
	if err != nil {
		return usdtRate
	}
	if jsondata.Code != 0 {
		return usdtRate
	}
	r := StringToFloat64(jsondata.Data[0].Price)
	r = Decimal(r - r*Rxf)
	return r
	//return lib.StringToFloat64(jsondata.Data[0].Price)
}

func DoLockAddr(addrid int64, money string, locktime int64) bool {
	dal.Lockaddress.Create(&entity.Lockaddress{
		Addrid: Int64ToInt32Ptr(addrid),
		Money:  &money,
		Ctime:  Int64ToInt32Ptr(time.Now().Unix()),
		Etime:  Int64ToInt32Ptr(time.Now().Unix() + locktime),
		Islock: Int64ToInt32Ptr(0),
	})
	return true
}

func CreateAddr() (string, string, string, int64) {
	lastAddr, _ := dal.Address.Last()
	var id int64
	if lastAddr == nil {
		id = 0
	} else {
		id = lastAddr.ID
	}
	index := id + 1
	trc, erc, key := GetAddr(uint64(index))
	return trc, erc, key, index
}

func Float64ToStringPtr(f float64) *string {
	s := strconv.FormatFloat(f, 'f', -1, 64)
	return &s
}

func TimeToTimeStamp(dateString string) int64 {
	layout := "2006-01-02 15:04:05"

	// 将日期字符串解析为 time.Time 类型
	parsedTime, err := time.Parse(layout, dateString)
	if err != nil {
		return 0
	}

	// 将 time.Time 转换为 Unix 时间戳（秒）
	unixTimestamp := parsedTime.Unix()
	return unixTimestamp
}

func UpdateIpWhiteList(ip_type int64) bool {
	var rule string
	if ip_type == 1 {
		//商户后台
		rule = "eac9a34d55384f91bfc7e44ce02005d7"
	} else if ip_type == 2 {
		//代理后台
		rule = "df7eaad572ce4a9cb62113acee617233"
	} else if ip_type == 3 {
		//商户API
		rule = "80f022eca55e4bce9112b83c4acaab2b"
	}
	url := "https://api.cloudflare.com/client/v4/accounts/********************************/rules/lists/" + rule + "/items"
	method := "PUT"
	//pdo := PDO()
	//defer log.Println("这个函数执行完了")
	//fmt.Println(ip_type)
	//sql := "select content,`type`,username from whitelist where deleted_at is NULL and `type`='" + fmt.Sprint(ip_type) + "';"
	//r, e := pdo.SelectAll(sql)
	//r, e := pdo.SelectAll("select content,`type`,username from whitelist where deleted_at is NULL and `type`=?;", ip_type)
	r, e := dal.Whitelist.Where(dal.Whitelist.DeletedAt.IsNull()).Where(dal.Whitelist.Type.Eq(cast.ToInt32(ip_type))).Find()
	if e != nil {
		fmt.Println(ip_type)
		//log.Println(e.Error())
		fmt.Println(e)
		return false
	}
	//log.Println(r)
	var resstr string
	if len(r) == 0 {
		resstr = "[]"
	} else {
		str := "["
		checkstr := ""
		for _, v := range r {
			if strings.Index(checkstr, *v.Content) != -1 {
				continue
			}
			str = str + `{"ip":"` + *v.Content + `","comment":"商户` + *v.Username + `"},`
			checkstr = checkstr + *v.Content + ","
		}
		content := str[:len(str)-1]
		resstr = content + "]"
	}
	payload := strings.NewReader(resstr)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, payload)

	if err != nil {
		fmt.Println(err)
		return false
	}
	req.Header.Add("X-Auth-Email", "<EMAIL>")
	req.Header.Add("X-Auth-Key", "26322cfae943dbfb840d8cc0394a04f3da2a2")
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return false
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return false
	}
	//fmt.Println(string(body))
	//如果body中success为true则表示成功
	if strings.Contains(string(body), " \"success\": true") {
		return true
	} else {
		fmt.Println(string(body))
		return false
	}
}
