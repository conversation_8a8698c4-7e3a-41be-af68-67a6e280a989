@echo off
echo Starting optimized build for Linux x64...

:: Set environment variables for Linux x64
set GOOS=linux
set GOARCH=amd64

:: Disable cgo to avoid compilation issues
set CGO_ENABLED=0

:: Check if Go is installed
where go >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Go compiler not found. Please ensure Go is installed and added to PATH.
    exit /b 1
)

:: Check if UPX exists in the current directory
if exist upx.exe (
    echo Local upx.exe found, will use for compression.
    set UPX_INSTALLED=1
) else (
    echo Warning: upx.exe not found in current directory, compression step will be skipped.
    set UPX_INSTALLED=0
)

:: Remove old build files
if exist build del build
if exist build_orig del build_orig

:: Compile with optimization for size, output as 'build_orig'
echo Building build (optimized for size)...
go build -trimpath -ldflags="-s -w" -o build_orig

:: Check if compilation succeeded
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    exit /b 1
)

:: Display size of the original build
echo Original file size:
dir build_orig | findstr "build"

:: If UPX exists, perform compression
if %UPX_INSTALLED%==1 (
    echo Using local UPX to compress the executable...
    copy build_orig build > nul
    .\upx.exe --best build

    if %ERRORLEVEL% neq 0 (
        echo UPX compression failed, but build succeeded.
        del build
    ) else (
        echo UPX compression completed successfully.
        echo Size after UPX compression:
        dir build | findstr "build"
    )
) else (
    :: No compression, just copy the original build
    copy build_orig build > nul
    echo No compression performed, copied to build.
)

:: Completion message
echo Build complete!
echo Original file: build_orig
if exist build echo Main executable: build

echo You can run the build on Linux with:
echo   ./build
pause