// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newSettlement(db *gorm.DB, opts ...gen.DOOption) settlement {
	_settlement := settlement{}

	_settlement.settlementDo.UseDB(db, opts...)
	_settlement.settlementDo.UseModel(&entity.Settlement{})

	tableName := _settlement.settlementDo.TableName()
	_settlement.ALL = field.NewAsterisk(tableName)
	_settlement.ID = field.NewInt64(tableName, "id")
	_settlement.CreatedAt = field.NewTime(tableName, "created_at")
	_settlement.UpdatedAt = field.NewTime(tableName, "updated_at")
	_settlement.DeletedAt = field.NewField(tableName, "deleted_at")
	_settlement.Type = field.NewInt32(tableName, "type")
	_settlement.Money = field.NewFloat32(tableName, "money")
	_settlement.Needmoney = field.NewString(tableName, "needmoney")
	_settlement.UserID = field.NewInt32(tableName, "user_id")
	_settlement.Status = field.NewInt32(tableName, "status")
	_settlement.Username = field.NewString(tableName, "username")
	_settlement.Ctime = field.NewInt32(tableName, "ctime")
	_settlement.Address = field.NewString(tableName, "address")
	_settlement.Chain = field.NewString(tableName, "chain")

	_settlement.fillFieldMap()

	return _settlement
}

type settlement struct {
	settlementDo

	ALL       field.Asterisk
	ID        field.Int64
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Type      field.Int32   // 1为商户2为代理
	Money     field.Float32 // 结算金额
	Needmoney field.String  // 结算后地址金额
	UserID    field.Int32   // 商户或代理id
	Status    field.Int32   // 0待处理
	Username  field.String  // 商户或代理用户名
	Ctime     field.Int32
	Address   field.String // 收款地址
	Chain     field.String // 链

	fieldMap map[string]field.Expr
}

func (s settlement) Table(newTableName string) *settlement {
	s.settlementDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s settlement) As(alias string) *settlement {
	s.settlementDo.DO = *(s.settlementDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *settlement) updateTableName(table string) *settlement {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")
	s.Type = field.NewInt32(table, "type")
	s.Money = field.NewFloat32(table, "money")
	s.Needmoney = field.NewString(table, "needmoney")
	s.UserID = field.NewInt32(table, "user_id")
	s.Status = field.NewInt32(table, "status")
	s.Username = field.NewString(table, "username")
	s.Ctime = field.NewInt32(table, "ctime")
	s.Address = field.NewString(table, "address")
	s.Chain = field.NewString(table, "chain")

	s.fillFieldMap()

	return s
}

func (s *settlement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *settlement) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["id"] = s.ID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["type"] = s.Type
	s.fieldMap["money"] = s.Money
	s.fieldMap["needmoney"] = s.Needmoney
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["status"] = s.Status
	s.fieldMap["username"] = s.Username
	s.fieldMap["ctime"] = s.Ctime
	s.fieldMap["address"] = s.Address
	s.fieldMap["chain"] = s.Chain
}

func (s settlement) clone(db *gorm.DB) settlement {
	s.settlementDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s settlement) replaceDB(db *gorm.DB) settlement {
	s.settlementDo.ReplaceDB(db)
	return s
}

type settlementDo struct{ gen.DO }

func (s settlementDo) Debug() *settlementDo {
	return s.withDO(s.DO.Debug())
}

func (s settlementDo) WithContext(ctx context.Context) *settlementDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s settlementDo) ReadDB() *settlementDo {
	return s.Clauses(dbresolver.Read)
}

func (s settlementDo) WriteDB() *settlementDo {
	return s.Clauses(dbresolver.Write)
}

func (s settlementDo) Session(config *gorm.Session) *settlementDo {
	return s.withDO(s.DO.Session(config))
}

func (s settlementDo) Clauses(conds ...clause.Expression) *settlementDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s settlementDo) Returning(value interface{}, columns ...string) *settlementDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s settlementDo) Not(conds ...gen.Condition) *settlementDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s settlementDo) Or(conds ...gen.Condition) *settlementDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s settlementDo) Select(conds ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s settlementDo) Where(conds ...gen.Condition) *settlementDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s settlementDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *settlementDo {
	return s.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (s settlementDo) Order(conds ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s settlementDo) Distinct(cols ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s settlementDo) Omit(cols ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s settlementDo) Join(table schema.Tabler, on ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s settlementDo) LeftJoin(table schema.Tabler, on ...field.Expr) *settlementDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s settlementDo) RightJoin(table schema.Tabler, on ...field.Expr) *settlementDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s settlementDo) Group(cols ...field.Expr) *settlementDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s settlementDo) Having(conds ...gen.Condition) *settlementDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s settlementDo) Limit(limit int) *settlementDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s settlementDo) Offset(offset int) *settlementDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s settlementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *settlementDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s settlementDo) Unscoped() *settlementDo {
	return s.withDO(s.DO.Unscoped())
}

func (s settlementDo) Create(values ...*entity.Settlement) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s settlementDo) CreateInBatches(values []*entity.Settlement, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s settlementDo) Save(values ...*entity.Settlement) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s settlementDo) First() (*entity.Settlement, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Settlement), nil
	}
}

func (s settlementDo) Take() (*entity.Settlement, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Settlement), nil
	}
}

func (s settlementDo) Last() (*entity.Settlement, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Settlement), nil
	}
}

func (s settlementDo) Find() ([]*entity.Settlement, error) {
	result, err := s.DO.Find()
	return result.([]*entity.Settlement), err
}

func (s settlementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Settlement, err error) {
	buf := make([]*entity.Settlement, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s settlementDo) FindInBatches(result *[]*entity.Settlement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s settlementDo) Attrs(attrs ...field.AssignExpr) *settlementDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s settlementDo) Assign(attrs ...field.AssignExpr) *settlementDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s settlementDo) Joins(fields ...field.RelationField) *settlementDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s settlementDo) Preload(fields ...field.RelationField) *settlementDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s settlementDo) FirstOrInit() (*entity.Settlement, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Settlement), nil
	}
}

func (s settlementDo) FirstOrCreate() (*entity.Settlement, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Settlement), nil
	}
}

func (s settlementDo) FindByPage(offset int, limit int) (result []*entity.Settlement, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s settlementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s settlementDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s settlementDo) Delete(models ...*entity.Settlement) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *settlementDo) withDO(do gen.Dao) *settlementDo {
	s.DO = *do.(*gen.DO)
	return s
}
