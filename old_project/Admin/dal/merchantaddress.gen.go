// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newMerchantaddress(db *gorm.DB, opts ...gen.DOOption) merchantaddress {
	_merchantaddress := merchantaddress{}

	_merchantaddress.merchantaddressDo.UseDB(db, opts...)
	_merchantaddress.merchantaddressDo.UseModel(&entity.Merchantaddress{})

	tableName := _merchantaddress.merchantaddressDo.TableName()
	_merchantaddress.ALL = field.NewAsterisk(tableName)
	_merchantaddress.ID = field.NewInt32(tableName, "id")
	_merchantaddress.Address = field.NewString(tableName, "address")
	_merchantaddress.MerchantID = field.NewInt32(tableName, "merchant_id")
	_merchantaddress.Ctime = field.NewInt32(tableName, "ctime")
	_merchantaddress.DelFlg = field.NewInt32(tableName, "del_flg")

	_merchantaddress.fillFieldMap()

	return _merchantaddress
}

type merchantaddress struct {
	merchantaddressDo

	ALL        field.Asterisk
	ID         field.Int32
	Address    field.String
	MerchantID field.Int32
	Ctime      field.Int32
	DelFlg     field.Int32

	fieldMap map[string]field.Expr
}

func (m merchantaddress) Table(newTableName string) *merchantaddress {
	m.merchantaddressDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchantaddress) As(alias string) *merchantaddress {
	m.merchantaddressDo.DO = *(m.merchantaddressDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchantaddress) updateTableName(table string) *merchantaddress {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.Address = field.NewString(table, "address")
	m.MerchantID = field.NewInt32(table, "merchant_id")
	m.Ctime = field.NewInt32(table, "ctime")
	m.DelFlg = field.NewInt32(table, "del_flg")

	m.fillFieldMap()

	return m
}

func (m *merchantaddress) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchantaddress) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["id"] = m.ID
	m.fieldMap["address"] = m.Address
	m.fieldMap["merchant_id"] = m.MerchantID
	m.fieldMap["ctime"] = m.Ctime
	m.fieldMap["del_flg"] = m.DelFlg
}

func (m merchantaddress) clone(db *gorm.DB) merchantaddress {
	m.merchantaddressDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchantaddress) replaceDB(db *gorm.DB) merchantaddress {
	m.merchantaddressDo.ReplaceDB(db)
	return m
}

type merchantaddressDo struct{ gen.DO }

func (m merchantaddressDo) Debug() *merchantaddressDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantaddressDo) WithContext(ctx context.Context) *merchantaddressDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantaddressDo) ReadDB() *merchantaddressDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantaddressDo) WriteDB() *merchantaddressDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantaddressDo) Session(config *gorm.Session) *merchantaddressDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantaddressDo) Clauses(conds ...clause.Expression) *merchantaddressDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantaddressDo) Returning(value interface{}, columns ...string) *merchantaddressDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantaddressDo) Not(conds ...gen.Condition) *merchantaddressDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantaddressDo) Or(conds ...gen.Condition) *merchantaddressDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantaddressDo) Select(conds ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantaddressDo) Where(conds ...gen.Condition) *merchantaddressDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantaddressDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *merchantaddressDo {
	return m.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (m merchantaddressDo) Order(conds ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantaddressDo) Distinct(cols ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantaddressDo) Omit(cols ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantaddressDo) Join(table schema.Tabler, on ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantaddressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantaddressDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantaddressDo) Group(cols ...field.Expr) *merchantaddressDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantaddressDo) Having(conds ...gen.Condition) *merchantaddressDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantaddressDo) Limit(limit int) *merchantaddressDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantaddressDo) Offset(offset int) *merchantaddressDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantaddressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantaddressDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantaddressDo) Unscoped() *merchantaddressDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantaddressDo) Create(values ...*entity.Merchantaddress) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantaddressDo) CreateInBatches(values []*entity.Merchantaddress, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantaddressDo) Save(values ...*entity.Merchantaddress) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantaddressDo) First() (*entity.Merchantaddress, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantaddress), nil
	}
}

func (m merchantaddressDo) Take() (*entity.Merchantaddress, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantaddress), nil
	}
}

func (m merchantaddressDo) Last() (*entity.Merchantaddress, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantaddress), nil
	}
}

func (m merchantaddressDo) Find() ([]*entity.Merchantaddress, error) {
	result, err := m.DO.Find()
	return result.([]*entity.Merchantaddress), err
}

func (m merchantaddressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Merchantaddress, err error) {
	buf := make([]*entity.Merchantaddress, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantaddressDo) FindInBatches(result *[]*entity.Merchantaddress, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantaddressDo) Attrs(attrs ...field.AssignExpr) *merchantaddressDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantaddressDo) Assign(attrs ...field.AssignExpr) *merchantaddressDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantaddressDo) Joins(fields ...field.RelationField) *merchantaddressDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantaddressDo) Preload(fields ...field.RelationField) *merchantaddressDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantaddressDo) FirstOrInit() (*entity.Merchantaddress, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantaddress), nil
	}
}

func (m merchantaddressDo) FirstOrCreate() (*entity.Merchantaddress, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchantaddress), nil
	}
}

func (m merchantaddressDo) FindByPage(offset int, limit int) (result []*entity.Merchantaddress, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantaddressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantaddressDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantaddressDo) Delete(models ...*entity.Merchantaddress) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantaddressDo) withDO(do gen.Dao) *merchantaddressDo {
	m.DO = *do.(*gen.DO)
	return m
}
