// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newAddress(db *gorm.DB, opts ...gen.DOOption) address {
	_address := address{}

	_address.addressDo.UseDB(db, opts...)
	_address.addressDo.UseModel(&entity.Address{})

	tableName := _address.addressDo.TableName()
	_address.ALL = field.NewAsterisk(tableName)
	_address.ID = field.NewInt64(tableName, "id")
	_address.Trxaddr = field.NewString(tableName, "trxaddr")
	_address.Ethaddr = field.NewString(tableName, "ethaddr")
	_address.Trx = field.NewFloat64(tableName, "trx")
	_address.UsdtTrc = field.NewFloat64(tableName, "usdt_trc")
	_address.UsdtErc = field.NewFloat64(tableName, "usdt_erc")
	_address.UsdtBsc = field.NewFloat64(tableName, "usdt_bsc")
	_address.Eth = field.NewFloat64(tableName, "eth")
	_address.Bnb = field.NewFloat64(tableName, "bnb")
	_address.Num = field.NewInt32(tableName, "num")
	_address.Ercnum = field.NewInt32(tableName, "ercnum")
	_address.Bscnum = field.NewInt32(tableName, "bscnum")
	_address.Lock = field.NewInt32(tableName, "lock")
	_address.Leaseid = field.NewString(tableName, "leaseid")
	_address.Key = field.NewString(tableName, "key")
	_address.Locktime = field.NewInt64(tableName, "locktime")
	_address.Userid = field.NewString(tableName, "userid")
	_address.Callbackurl = field.NewString(tableName, "callbackurl")
	_address.MerchantID = field.NewInt32(tableName, "merchant_id")
	_address.MerchantName = field.NewString(tableName, "merchant_name")
	_address.DelFlg = field.NewInt32(tableName, "del_flg")
	_address.Trxlock = field.NewInt32(tableName, "trxlock")
	_address.Ethlock = field.NewInt32(tableName, "ethlock")
	_address.Bnblock = field.NewInt32(tableName, "bnblock")
	_address.Ethhash = field.NewString(tableName, "ethhash")
	_address.Usdthash = field.NewString(tableName, "usdthash")
	_address.TrxStatus = field.NewInt32(tableName, "trx_status")
	_address.EthStatus = field.NewInt32(tableName, "eth_status")
	_address.BscStatus = field.NewInt32(tableName, "bsc_status")
	_address.UpdateMoney = field.NewInt32(tableName, "update_money")
	_address.Trcsid = field.NewInt32(tableName, "trcsid")
	_address.Ercsid = field.NewInt32(tableName, "ercsid")
	_address.Bscsid = field.NewInt32(tableName, "bscsid")

	_address.fillFieldMap()

	return _address
}

type address struct {
	addressDo

	ALL          field.Asterisk
	ID           field.Int64
	Trxaddr      field.String  // TRX地址
	Ethaddr      field.String  // ETH地址
	Trx          field.Float64 // TRX余额
	UsdtTrc      field.Float64 // 波场链USDT余额
	UsdtErc      field.Float64 // 以太坊链USDT余额
	UsdtBsc      field.Float64 // 币安链USDT余额
	Eth          field.Float64 // ETH余额
	Bnb          field.Float64 // BNB余额
	Num          field.Int32   // 笔数
	Ercnum       field.Int32   // erc笔数
	Bscnum       field.Int32   // bsc笔数
	Lock         field.Int32
	Leaseid      field.String
	Key          field.String // 私钥
	Locktime     field.Int64  // 锁定时间
	Userid       field.String // 商户绑定用户ID
	Callbackurl  field.String // 回调地址
	MerchantID   field.Int32  // 商户id
	MerchantName field.String // 商户名
	DelFlg       field.Int32
	Trxlock      field.Int32 // 用于trc归集 记录状态
	Ethlock      field.Int32 // 用于erc归集 记录状态
	Bnblock      field.Int32 // 用于bsc归集 记录状态
	Ethhash      field.String
	Usdthash     field.String
	TrxStatus    field.Int32
	EthStatus    field.Int32
	BscStatus    field.Int32
	UpdateMoney  field.Int32
	Trcsid       field.Int32
	Ercsid       field.Int32
	Bscsid       field.Int32

	fieldMap map[string]field.Expr
}

func (a address) Table(newTableName string) *address {
	a.addressDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a address) As(alias string) *address {
	a.addressDo.DO = *(a.addressDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *address) updateTableName(table string) *address {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.Trxaddr = field.NewString(table, "trxaddr")
	a.Ethaddr = field.NewString(table, "ethaddr")
	a.Trx = field.NewFloat64(table, "trx")
	a.UsdtTrc = field.NewFloat64(table, "usdt_trc")
	a.UsdtErc = field.NewFloat64(table, "usdt_erc")
	a.UsdtBsc = field.NewFloat64(table, "usdt_bsc")
	a.Eth = field.NewFloat64(table, "eth")
	a.Bnb = field.NewFloat64(table, "bnb")
	a.Num = field.NewInt32(table, "num")
	a.Ercnum = field.NewInt32(table, "ercnum")
	a.Bscnum = field.NewInt32(table, "bscnum")
	a.Lock = field.NewInt32(table, "lock")
	a.Leaseid = field.NewString(table, "leaseid")
	a.Key = field.NewString(table, "key")
	a.Locktime = field.NewInt64(table, "locktime")
	a.Userid = field.NewString(table, "userid")
	a.Callbackurl = field.NewString(table, "callbackurl")
	a.MerchantID = field.NewInt32(table, "merchant_id")
	a.MerchantName = field.NewString(table, "merchant_name")
	a.DelFlg = field.NewInt32(table, "del_flg")
	a.Trxlock = field.NewInt32(table, "trxlock")
	a.Ethlock = field.NewInt32(table, "ethlock")
	a.Bnblock = field.NewInt32(table, "bnblock")
	a.Ethhash = field.NewString(table, "ethhash")
	a.Usdthash = field.NewString(table, "usdthash")
	a.TrxStatus = field.NewInt32(table, "trx_status")
	a.EthStatus = field.NewInt32(table, "eth_status")
	a.BscStatus = field.NewInt32(table, "bsc_status")
	a.UpdateMoney = field.NewInt32(table, "update_money")
	a.Trcsid = field.NewInt32(table, "trcsid")
	a.Ercsid = field.NewInt32(table, "ercsid")
	a.Bscsid = field.NewInt32(table, "bscsid")

	a.fillFieldMap()

	return a
}

func (a *address) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *address) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 33)
	a.fieldMap["id"] = a.ID
	a.fieldMap["trxaddr"] = a.Trxaddr
	a.fieldMap["ethaddr"] = a.Ethaddr
	a.fieldMap["trx"] = a.Trx
	a.fieldMap["usdt_trc"] = a.UsdtTrc
	a.fieldMap["usdt_erc"] = a.UsdtErc
	a.fieldMap["usdt_bsc"] = a.UsdtBsc
	a.fieldMap["eth"] = a.Eth
	a.fieldMap["bnb"] = a.Bnb
	a.fieldMap["num"] = a.Num
	a.fieldMap["ercnum"] = a.Ercnum
	a.fieldMap["bscnum"] = a.Bscnum
	a.fieldMap["lock"] = a.Lock
	a.fieldMap["leaseid"] = a.Leaseid
	a.fieldMap["key"] = a.Key
	a.fieldMap["locktime"] = a.Locktime
	a.fieldMap["userid"] = a.Userid
	a.fieldMap["callbackurl"] = a.Callbackurl
	a.fieldMap["merchant_id"] = a.MerchantID
	a.fieldMap["merchant_name"] = a.MerchantName
	a.fieldMap["del_flg"] = a.DelFlg
	a.fieldMap["trxlock"] = a.Trxlock
	a.fieldMap["ethlock"] = a.Ethlock
	a.fieldMap["bnblock"] = a.Bnblock
	a.fieldMap["ethhash"] = a.Ethhash
	a.fieldMap["usdthash"] = a.Usdthash
	a.fieldMap["trx_status"] = a.TrxStatus
	a.fieldMap["eth_status"] = a.EthStatus
	a.fieldMap["bsc_status"] = a.BscStatus
	a.fieldMap["update_money"] = a.UpdateMoney
	a.fieldMap["trcsid"] = a.Trcsid
	a.fieldMap["ercsid"] = a.Ercsid
	a.fieldMap["bscsid"] = a.Bscsid
}

func (a address) clone(db *gorm.DB) address {
	a.addressDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a address) replaceDB(db *gorm.DB) address {
	a.addressDo.ReplaceDB(db)
	return a
}

type addressDo struct{ gen.DO }

func (a addressDo) Debug() *addressDo {
	return a.withDO(a.DO.Debug())
}

func (a addressDo) WithContext(ctx context.Context) *addressDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a addressDo) ReadDB() *addressDo {
	return a.Clauses(dbresolver.Read)
}

func (a addressDo) WriteDB() *addressDo {
	return a.Clauses(dbresolver.Write)
}

func (a addressDo) Session(config *gorm.Session) *addressDo {
	return a.withDO(a.DO.Session(config))
}

func (a addressDo) Clauses(conds ...clause.Expression) *addressDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a addressDo) Returning(value interface{}, columns ...string) *addressDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a addressDo) Not(conds ...gen.Condition) *addressDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a addressDo) Or(conds ...gen.Condition) *addressDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a addressDo) Select(conds ...field.Expr) *addressDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a addressDo) Where(conds ...gen.Condition) *addressDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a addressDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *addressDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a addressDo) Order(conds ...field.Expr) *addressDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a addressDo) Distinct(cols ...field.Expr) *addressDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a addressDo) Omit(cols ...field.Expr) *addressDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a addressDo) Join(table schema.Tabler, on ...field.Expr) *addressDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a addressDo) LeftJoin(table schema.Tabler, on ...field.Expr) *addressDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a addressDo) RightJoin(table schema.Tabler, on ...field.Expr) *addressDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a addressDo) Group(cols ...field.Expr) *addressDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a addressDo) Having(conds ...gen.Condition) *addressDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a addressDo) Limit(limit int) *addressDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a addressDo) Offset(offset int) *addressDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a addressDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *addressDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a addressDo) Unscoped() *addressDo {
	return a.withDO(a.DO.Unscoped())
}

func (a addressDo) Create(values ...*entity.Address) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a addressDo) CreateInBatches(values []*entity.Address, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a addressDo) Save(values ...*entity.Address) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a addressDo) First() (*entity.Address, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Address), nil
	}
}

func (a addressDo) Take() (*entity.Address, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Address), nil
	}
}

func (a addressDo) Last() (*entity.Address, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Address), nil
	}
}

func (a addressDo) Find() ([]*entity.Address, error) {
	result, err := a.DO.Find()
	return result.([]*entity.Address), err
}

func (a addressDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Address, err error) {
	buf := make([]*entity.Address, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a addressDo) FindInBatches(result *[]*entity.Address, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a addressDo) Attrs(attrs ...field.AssignExpr) *addressDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a addressDo) Assign(attrs ...field.AssignExpr) *addressDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a addressDo) Joins(fields ...field.RelationField) *addressDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a addressDo) Preload(fields ...field.RelationField) *addressDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a addressDo) FirstOrInit() (*entity.Address, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Address), nil
	}
}

func (a addressDo) FirstOrCreate() (*entity.Address, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Address), nil
	}
}

func (a addressDo) FindByPage(offset int, limit int) (result []*entity.Address, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a addressDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a addressDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a addressDo) Delete(models ...*entity.Address) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *addressDo) withDO(do gen.Dao) *addressDo {
	a.DO = *do.(*gen.DO)
	return a
}
