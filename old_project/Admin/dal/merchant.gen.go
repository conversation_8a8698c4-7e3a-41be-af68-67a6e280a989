// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newMerchant(db *gorm.DB, opts ...gen.DOOption) merchant {
	_merchant := merchant{}

	_merchant.merchantDo.UseDB(db, opts...)
	_merchant.merchantDo.UseModel(&entity.Merchant{})

	tableName := _merchant.merchantDo.TableName()
	_merchant.ALL = field.NewAsterisk(tableName)
	_merchant.ID = field.NewInt64(tableName, "id")
	_merchant.CreatedAt = field.NewTime(tableName, "created_at")
	_merchant.UpdatedAt = field.NewTime(tableName, "updated_at")
	_merchant.DeletedAt = field.NewField(tableName, "deleted_at")
	_merchant.Username = field.NewString(tableName, "username")
	_merchant.Password = field.NewString(tableName, "password")
	_merchant.Status = field.NewInt32(tableName, "status")
	_merchant.Fee = field.NewFloat64(tableName, "fee")
	_merchant.Ethfee = field.NewFloat64(tableName, "ethfee")
	_merchant.Bscfee = field.NewFloat64(tableName, "bscfee")
	_merchant.Money = field.NewFloat64(tableName, "money")
	_merchant.LockMoney = field.NewFloat64(tableName, "lock_money")
	_merchant.FeeMoney = field.NewFloat64(tableName, "fee_money")
	_merchant.EthFeemoney = field.NewFloat64(tableName, "eth_feemoney")
	_merchant.ErcLockmoney = field.NewFloat64(tableName, "erc_lockmoney")
	_merchant.ErcMoney = field.NewFloat64(tableName, "erc_money")
	_merchant.BscMoney = field.NewFloat64(tableName, "bsc_money")
	_merchant.BscLockmoney = field.NewFloat64(tableName, "bsc_lockmoney")
	_merchant.BscFeemoney = field.NewFloat64(tableName, "bsc_feemoney")
	_merchant.Secret = field.NewString(tableName, "secret")
	_merchant.Paysecret = field.NewString(tableName, "paysecret")
	_merchant.AgentID = field.NewInt32(tableName, "agent_id")
	_merchant.AgentName = field.NewString(tableName, "agent_name")
	_merchant.EthAgentFee = field.NewFloat64(tableName, "eth_agent_fee")
	_merchant.Appkey = field.NewString(tableName, "appkey")
	_merchant.Token = field.NewString(tableName, "token")
	_merchant.Credits = field.NewFloat64(tableName, "credits")
	_merchant.Rate = field.NewFloat64(tableName, "rate")
	_merchant.Ratetype = field.NewString(tableName, "ratetype")
	_merchant.Floatratetype = field.NewString(tableName, "floatratetype")
	_merchant.Floatratedvalue = field.NewString(tableName, "floatratedvalue")
	_merchant.AgentFee = field.NewFloat64(tableName, "agent_fee")
	_merchant.IsFloatdown = field.NewString(tableName, "is_floatdown")
	_merchant.Largeordertimeout = field.NewString(tableName, "largeordertimeout")
	_merchant.Smallordertimeout = field.NewString(tableName, "smallordertimeout")
	_merchant.Largeorderlocktime = field.NewString(tableName, "largeorderlocktime")
	_merchant.Smallorderlocktime = field.NewString(tableName, "smallorderlocktime")
	_merchant.Largeorderamount = field.NewString(tableName, "largeorderamount")
	_merchant.AddressID = field.NewInt32(tableName, "address_id")
	_merchant.TrcStatus = field.NewInt32(tableName, "trc_status")
	_merchant.ErcStatus = field.NewInt32(tableName, "erc_status")
	_merchant.BscStatus = field.NewInt32(tableName, "bsc_status")
	_merchant.GatherType = field.NewInt32(tableName, "gather_type")
	_merchant.Trcneedmoney = field.NewString(tableName, "trcneedmoney")
	_merchant.Ercneedmoney = field.NewString(tableName, "ercneedmoney")
	_merchant.Bscneedmoney = field.NewString(tableName, "bscneedmoney")
	_merchant.IsLimitAmount = field.NewInt32(tableName, "is_limit_amount")
	_merchant.IsTieredRate = field.NewInt32(tableName, "is_tiered_rate")
	_merchant.CommissionPrice = field.NewFloat64(tableName, "commission_price")

	_merchant.fillFieldMap()

	return _merchant
}

type merchant struct {
	merchantDo

	ALL                field.Asterisk
	ID                 field.Int64
	CreatedAt          field.Time
	UpdatedAt          field.Time
	DeletedAt          field.Field
	Username           field.String  // 用户名
	Password           field.String  // 密码
	Status             field.Int32   // 状态 启用0 禁用1
	Fee                field.Float64 // 费率
	Ethfee             field.Float64 // eth费率
	Bscfee             field.Float64 // bsc费率
	Money              field.Float64 // TRC余额
	LockMoney          field.Float64 // TRC锁定余额
	FeeMoney           field.Float64 // TRC手续费余额(TRX)
	EthFeemoney        field.Float64 // ERC手续费余额(ETH)
	ErcLockmoney       field.Float64 // ERC锁定余额
	ErcMoney           field.Float64 // ERC余额
	BscMoney           field.Float64 // BSC余额
	BscLockmoney       field.Float64 // BSC锁定余额
	BscFeemoney        field.Float64 // BSC手续费余额(BNB)
	Secret             field.String  // Google密钥
	Paysecret          field.String  // 下发Google密钥
	AgentID            field.Int32   // 所属代理id
	AgentName          field.String  // 所属代理名
	EthAgentFee        field.Float64 // eth代理费率
	Appkey             field.String  // 密钥
	Token              field.String  // token
	Credits            field.Float64 // 信用额度(可透支手续费)
	Rate               field.Float64 // 固定汇率
	Ratetype           field.String  // 汇率类型 1固定 2浮动
	Floatratetype      field.String  // 浮动汇率差值类型 1百分比 2固定数值
	Floatratedvalue    field.String  // 浮动汇率差值
	AgentFee           field.Float64 // 代理费率
	IsFloatdown        field.String  // 是否开启下浮
	Largeordertimeout  field.String  // 大额订单超时时间
	Smallordertimeout  field.String  // 小额订单超时时间
	Largeorderlocktime field.String  // 大额订单trx锁定时间
	Smallorderlocktime field.String  // 小额订单trx锁定时间
	Largeorderamount   field.String  // 大额订单金额(>)
	AddressID          field.Int32   // 地址ID
	TrcStatus          field.Int32   // TRC归集状态 0未执行归集 1等待归集 2归集执行中
	ErcStatus          field.Int32   // ERC归集状态 0未执行归集 1等待归集 2归集执行中
	BscStatus          field.Int32   // BSC归集状态 0未执行归集 1等待归集 2归集执行中
	GatherType         field.Int32   // 归集类型 1自动归集 2下发时归集
	Trcneedmoney       field.String  // 归集用
	Ercneedmoney       field.String  // 归集用
	Bscneedmoney       field.String  // 归集用
	IsLimitAmount      field.Int32   // 下发是否限制最低订单金额
	IsTieredRate       field.Int32   // 是否是阶梯汇率 0否 1是
	CommissionPrice    field.Float64 // 商户固定按笔数的手续费

	fieldMap map[string]field.Expr
}

func (m merchant) Table(newTableName string) *merchant {
	m.merchantDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m merchant) As(alias string) *merchant {
	m.merchantDo.DO = *(m.merchantDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *merchant) updateTableName(table string) *merchant {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.Username = field.NewString(table, "username")
	m.Password = field.NewString(table, "password")
	m.Status = field.NewInt32(table, "status")
	m.Fee = field.NewFloat64(table, "fee")
	m.Ethfee = field.NewFloat64(table, "ethfee")
	m.Bscfee = field.NewFloat64(table, "bscfee")
	m.Money = field.NewFloat64(table, "money")
	m.LockMoney = field.NewFloat64(table, "lock_money")
	m.FeeMoney = field.NewFloat64(table, "fee_money")
	m.EthFeemoney = field.NewFloat64(table, "eth_feemoney")
	m.ErcLockmoney = field.NewFloat64(table, "erc_lockmoney")
	m.ErcMoney = field.NewFloat64(table, "erc_money")
	m.BscMoney = field.NewFloat64(table, "bsc_money")
	m.BscLockmoney = field.NewFloat64(table, "bsc_lockmoney")
	m.BscFeemoney = field.NewFloat64(table, "bsc_feemoney")
	m.Secret = field.NewString(table, "secret")
	m.Paysecret = field.NewString(table, "paysecret")
	m.AgentID = field.NewInt32(table, "agent_id")
	m.AgentName = field.NewString(table, "agent_name")
	m.EthAgentFee = field.NewFloat64(table, "eth_agent_fee")
	m.Appkey = field.NewString(table, "appkey")
	m.Token = field.NewString(table, "token")
	m.Credits = field.NewFloat64(table, "credits")
	m.Rate = field.NewFloat64(table, "rate")
	m.Ratetype = field.NewString(table, "ratetype")
	m.Floatratetype = field.NewString(table, "floatratetype")
	m.Floatratedvalue = field.NewString(table, "floatratedvalue")
	m.AgentFee = field.NewFloat64(table, "agent_fee")
	m.IsFloatdown = field.NewString(table, "is_floatdown")
	m.Largeordertimeout = field.NewString(table, "largeordertimeout")
	m.Smallordertimeout = field.NewString(table, "smallordertimeout")
	m.Largeorderlocktime = field.NewString(table, "largeorderlocktime")
	m.Smallorderlocktime = field.NewString(table, "smallorderlocktime")
	m.Largeorderamount = field.NewString(table, "largeorderamount")
	m.AddressID = field.NewInt32(table, "address_id")
	m.TrcStatus = field.NewInt32(table, "trc_status")
	m.ErcStatus = field.NewInt32(table, "erc_status")
	m.BscStatus = field.NewInt32(table, "bsc_status")
	m.GatherType = field.NewInt32(table, "gather_type")
	m.Trcneedmoney = field.NewString(table, "trcneedmoney")
	m.Ercneedmoney = field.NewString(table, "ercneedmoney")
	m.Bscneedmoney = field.NewString(table, "bscneedmoney")
	m.IsLimitAmount = field.NewInt32(table, "is_limit_amount")
	m.IsTieredRate = field.NewInt32(table, "is_tiered_rate")
	m.CommissionPrice = field.NewFloat64(table, "commission_price")

	m.fillFieldMap()

	return m
}

func (m *merchant) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *merchant) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 49)
	m.fieldMap["id"] = m.ID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["username"] = m.Username
	m.fieldMap["password"] = m.Password
	m.fieldMap["status"] = m.Status
	m.fieldMap["fee"] = m.Fee
	m.fieldMap["ethfee"] = m.Ethfee
	m.fieldMap["bscfee"] = m.Bscfee
	m.fieldMap["money"] = m.Money
	m.fieldMap["lock_money"] = m.LockMoney
	m.fieldMap["fee_money"] = m.FeeMoney
	m.fieldMap["eth_feemoney"] = m.EthFeemoney
	m.fieldMap["erc_lockmoney"] = m.ErcLockmoney
	m.fieldMap["erc_money"] = m.ErcMoney
	m.fieldMap["bsc_money"] = m.BscMoney
	m.fieldMap["bsc_lockmoney"] = m.BscLockmoney
	m.fieldMap["bsc_feemoney"] = m.BscFeemoney
	m.fieldMap["secret"] = m.Secret
	m.fieldMap["paysecret"] = m.Paysecret
	m.fieldMap["agent_id"] = m.AgentID
	m.fieldMap["agent_name"] = m.AgentName
	m.fieldMap["eth_agent_fee"] = m.EthAgentFee
	m.fieldMap["appkey"] = m.Appkey
	m.fieldMap["token"] = m.Token
	m.fieldMap["credits"] = m.Credits
	m.fieldMap["rate"] = m.Rate
	m.fieldMap["ratetype"] = m.Ratetype
	m.fieldMap["floatratetype"] = m.Floatratetype
	m.fieldMap["floatratedvalue"] = m.Floatratedvalue
	m.fieldMap["agent_fee"] = m.AgentFee
	m.fieldMap["is_floatdown"] = m.IsFloatdown
	m.fieldMap["largeordertimeout"] = m.Largeordertimeout
	m.fieldMap["smallordertimeout"] = m.Smallordertimeout
	m.fieldMap["largeorderlocktime"] = m.Largeorderlocktime
	m.fieldMap["smallorderlocktime"] = m.Smallorderlocktime
	m.fieldMap["largeorderamount"] = m.Largeorderamount
	m.fieldMap["address_id"] = m.AddressID
	m.fieldMap["trc_status"] = m.TrcStatus
	m.fieldMap["erc_status"] = m.ErcStatus
	m.fieldMap["bsc_status"] = m.BscStatus
	m.fieldMap["gather_type"] = m.GatherType
	m.fieldMap["trcneedmoney"] = m.Trcneedmoney
	m.fieldMap["ercneedmoney"] = m.Ercneedmoney
	m.fieldMap["bscneedmoney"] = m.Bscneedmoney
	m.fieldMap["is_limit_amount"] = m.IsLimitAmount
	m.fieldMap["is_tiered_rate"] = m.IsTieredRate
	m.fieldMap["commission_price"] = m.CommissionPrice
}

func (m merchant) clone(db *gorm.DB) merchant {
	m.merchantDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m merchant) replaceDB(db *gorm.DB) merchant {
	m.merchantDo.ReplaceDB(db)
	return m
}

type merchantDo struct{ gen.DO }

func (m merchantDo) Debug() *merchantDo {
	return m.withDO(m.DO.Debug())
}

func (m merchantDo) WithContext(ctx context.Context) *merchantDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m merchantDo) ReadDB() *merchantDo {
	return m.Clauses(dbresolver.Read)
}

func (m merchantDo) WriteDB() *merchantDo {
	return m.Clauses(dbresolver.Write)
}

func (m merchantDo) Session(config *gorm.Session) *merchantDo {
	return m.withDO(m.DO.Session(config))
}

func (m merchantDo) Clauses(conds ...clause.Expression) *merchantDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m merchantDo) Returning(value interface{}, columns ...string) *merchantDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m merchantDo) Not(conds ...gen.Condition) *merchantDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m merchantDo) Or(conds ...gen.Condition) *merchantDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m merchantDo) Select(conds ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m merchantDo) Where(conds ...gen.Condition) *merchantDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m merchantDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *merchantDo {
	return m.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (m merchantDo) Order(conds ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m merchantDo) Distinct(cols ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m merchantDo) Omit(cols ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m merchantDo) Join(table schema.Tabler, on ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m merchantDo) LeftJoin(table schema.Tabler, on ...field.Expr) *merchantDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m merchantDo) RightJoin(table schema.Tabler, on ...field.Expr) *merchantDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m merchantDo) Group(cols ...field.Expr) *merchantDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m merchantDo) Having(conds ...gen.Condition) *merchantDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m merchantDo) Limit(limit int) *merchantDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m merchantDo) Offset(offset int) *merchantDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m merchantDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *merchantDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m merchantDo) Unscoped() *merchantDo {
	return m.withDO(m.DO.Unscoped())
}

func (m merchantDo) Create(values ...*entity.Merchant) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m merchantDo) CreateInBatches(values []*entity.Merchant, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m merchantDo) Save(values ...*entity.Merchant) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m merchantDo) First() (*entity.Merchant, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchant), nil
	}
}

func (m merchantDo) Take() (*entity.Merchant, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchant), nil
	}
}

func (m merchantDo) Last() (*entity.Merchant, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchant), nil
	}
}

func (m merchantDo) Find() ([]*entity.Merchant, error) {
	result, err := m.DO.Find()
	return result.([]*entity.Merchant), err
}

func (m merchantDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Merchant, err error) {
	buf := make([]*entity.Merchant, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m merchantDo) FindInBatches(result *[]*entity.Merchant, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m merchantDo) Attrs(attrs ...field.AssignExpr) *merchantDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m merchantDo) Assign(attrs ...field.AssignExpr) *merchantDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m merchantDo) Joins(fields ...field.RelationField) *merchantDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m merchantDo) Preload(fields ...field.RelationField) *merchantDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m merchantDo) FirstOrInit() (*entity.Merchant, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchant), nil
	}
}

func (m merchantDo) FirstOrCreate() (*entity.Merchant, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Merchant), nil
	}
}

func (m merchantDo) FindByPage(offset int, limit int) (result []*entity.Merchant, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m merchantDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m merchantDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m merchantDo) Delete(models ...*entity.Merchant) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *merchantDo) withDO(do gen.Dao) *merchantDo {
	m.DO = *do.(*gen.DO)
	return m
}
