// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newLease(db *gorm.DB, opts ...gen.DOOption) lease {
	_lease := lease{}

	_lease.leaseDo.UseDB(db, opts...)
	_lease.leaseDo.UseModel(&entity.Lease{})

	tableName := _lease.leaseDo.TableName()
	_lease.ALL = field.NewAsterisk(tableName)
	_lease.ID = field.NewInt32(tableName, "id")
	_lease.Addressid = field.NewInt32(tableName, "addressid")
	_lease.Leaseid = field.NewString(tableName, "leaseid")
	_lease.Orderid = field.NewInt32(tableName, "orderid")
	_lease.Status = field.NewInt32(tableName, "status")
	_lease.Ctime = field.NewInt32(tableName, "ctime")

	_lease.fillFieldMap()

	return _lease
}

type lease struct {
	leaseDo

	ALL       field.Asterisk
	ID        field.Int32
	Addressid field.Int32
	Leaseid   field.String
	Orderid   field.Int32
	Status    field.Int32
	Ctime     field.Int32

	fieldMap map[string]field.Expr
}

func (l lease) Table(newTableName string) *lease {
	l.leaseDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l lease) As(alias string) *lease {
	l.leaseDo.DO = *(l.leaseDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *lease) updateTableName(table string) *lease {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt32(table, "id")
	l.Addressid = field.NewInt32(table, "addressid")
	l.Leaseid = field.NewString(table, "leaseid")
	l.Orderid = field.NewInt32(table, "orderid")
	l.Status = field.NewInt32(table, "status")
	l.Ctime = field.NewInt32(table, "ctime")

	l.fillFieldMap()

	return l
}

func (l *lease) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *lease) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 6)
	l.fieldMap["id"] = l.ID
	l.fieldMap["addressid"] = l.Addressid
	l.fieldMap["leaseid"] = l.Leaseid
	l.fieldMap["orderid"] = l.Orderid
	l.fieldMap["status"] = l.Status
	l.fieldMap["ctime"] = l.Ctime
}

func (l lease) clone(db *gorm.DB) lease {
	l.leaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l lease) replaceDB(db *gorm.DB) lease {
	l.leaseDo.ReplaceDB(db)
	return l
}

type leaseDo struct{ gen.DO }

func (l leaseDo) Debug() *leaseDo {
	return l.withDO(l.DO.Debug())
}

func (l leaseDo) WithContext(ctx context.Context) *leaseDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l leaseDo) ReadDB() *leaseDo {
	return l.Clauses(dbresolver.Read)
}

func (l leaseDo) WriteDB() *leaseDo {
	return l.Clauses(dbresolver.Write)
}

func (l leaseDo) Session(config *gorm.Session) *leaseDo {
	return l.withDO(l.DO.Session(config))
}

func (l leaseDo) Clauses(conds ...clause.Expression) *leaseDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l leaseDo) Returning(value interface{}, columns ...string) *leaseDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l leaseDo) Not(conds ...gen.Condition) *leaseDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l leaseDo) Or(conds ...gen.Condition) *leaseDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l leaseDo) Select(conds ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l leaseDo) Where(conds ...gen.Condition) *leaseDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l leaseDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *leaseDo {
	return l.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (l leaseDo) Order(conds ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l leaseDo) Distinct(cols ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l leaseDo) Omit(cols ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l leaseDo) Join(table schema.Tabler, on ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l leaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) *leaseDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l leaseDo) RightJoin(table schema.Tabler, on ...field.Expr) *leaseDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l leaseDo) Group(cols ...field.Expr) *leaseDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l leaseDo) Having(conds ...gen.Condition) *leaseDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l leaseDo) Limit(limit int) *leaseDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l leaseDo) Offset(offset int) *leaseDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l leaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *leaseDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l leaseDo) Unscoped() *leaseDo {
	return l.withDO(l.DO.Unscoped())
}

func (l leaseDo) Create(values ...*entity.Lease) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l leaseDo) CreateInBatches(values []*entity.Lease, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l leaseDo) Save(values ...*entity.Lease) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l leaseDo) First() (*entity.Lease, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lease), nil
	}
}

func (l leaseDo) Take() (*entity.Lease, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lease), nil
	}
}

func (l leaseDo) Last() (*entity.Lease, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lease), nil
	}
}

func (l leaseDo) Find() ([]*entity.Lease, error) {
	result, err := l.DO.Find()
	return result.([]*entity.Lease), err
}

func (l leaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Lease, err error) {
	buf := make([]*entity.Lease, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l leaseDo) FindInBatches(result *[]*entity.Lease, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l leaseDo) Attrs(attrs ...field.AssignExpr) *leaseDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l leaseDo) Assign(attrs ...field.AssignExpr) *leaseDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l leaseDo) Joins(fields ...field.RelationField) *leaseDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l leaseDo) Preload(fields ...field.RelationField) *leaseDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l leaseDo) FirstOrInit() (*entity.Lease, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lease), nil
	}
}

func (l leaseDo) FirstOrCreate() (*entity.Lease, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Lease), nil
	}
}

func (l leaseDo) FindByPage(offset int, limit int) (result []*entity.Lease, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l leaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l leaseDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l leaseDo) Delete(models ...*entity.Lease) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *leaseDo) withDO(do gen.Dao) *leaseDo {
	l.DO = *do.(*gen.DO)
	return l
}
