// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newPermission(db *gorm.DB, opts ...gen.DOOption) permission {
	_permission := permission{}

	_permission.permissionDo.UseDB(db, opts...)
	_permission.permissionDo.UseModel(&entity.Permission{})

	tableName := _permission.permissionDo.TableName()
	_permission.ALL = field.NewAsterisk(tableName)
	_permission.ID = field.NewInt32(tableName, "id")
	_permission.Name = field.NewString(tableName, "name")
	_permission.Description = field.NewString(tableName, "description")
	_permission.Route = field.NewString(tableName, "route")
	_permission.Method = field.NewString(tableName, "method")
	_permission.Ctime = field.NewInt32(tableName, "ctime")
	_permission.DelFlg = field.NewInt32(tableName, "del_flg")

	_permission.fillFieldMap()

	return _permission
}

type permission struct {
	permissionDo

	ALL         field.Asterisk
	ID          field.Int32
	Name        field.String // 名称
	Description field.String // 描述
	Route       field.String // 路由
	Method      field.String // GET还是POST
	Ctime       field.Int32
	DelFlg      field.Int32

	fieldMap map[string]field.Expr
}

func (p permission) Table(newTableName string) *permission {
	p.permissionDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p permission) As(alias string) *permission {
	p.permissionDo.DO = *(p.permissionDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *permission) updateTableName(table string) *permission {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt32(table, "id")
	p.Name = field.NewString(table, "name")
	p.Description = field.NewString(table, "description")
	p.Route = field.NewString(table, "route")
	p.Method = field.NewString(table, "method")
	p.Ctime = field.NewInt32(table, "ctime")
	p.DelFlg = field.NewInt32(table, "del_flg")

	p.fillFieldMap()

	return p
}

func (p *permission) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *permission) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 7)
	p.fieldMap["id"] = p.ID
	p.fieldMap["name"] = p.Name
	p.fieldMap["description"] = p.Description
	p.fieldMap["route"] = p.Route
	p.fieldMap["method"] = p.Method
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["del_flg"] = p.DelFlg
}

func (p permission) clone(db *gorm.DB) permission {
	p.permissionDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p permission) replaceDB(db *gorm.DB) permission {
	p.permissionDo.ReplaceDB(db)
	return p
}

type permissionDo struct{ gen.DO }

func (p permissionDo) Debug() *permissionDo {
	return p.withDO(p.DO.Debug())
}

func (p permissionDo) WithContext(ctx context.Context) *permissionDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p permissionDo) ReadDB() *permissionDo {
	return p.Clauses(dbresolver.Read)
}

func (p permissionDo) WriteDB() *permissionDo {
	return p.Clauses(dbresolver.Write)
}

func (p permissionDo) Session(config *gorm.Session) *permissionDo {
	return p.withDO(p.DO.Session(config))
}

func (p permissionDo) Clauses(conds ...clause.Expression) *permissionDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p permissionDo) Returning(value interface{}, columns ...string) *permissionDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p permissionDo) Not(conds ...gen.Condition) *permissionDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p permissionDo) Or(conds ...gen.Condition) *permissionDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p permissionDo) Select(conds ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p permissionDo) Where(conds ...gen.Condition) *permissionDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p permissionDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *permissionDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p permissionDo) Order(conds ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p permissionDo) Distinct(cols ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p permissionDo) Omit(cols ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p permissionDo) Join(table schema.Tabler, on ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p permissionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *permissionDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p permissionDo) RightJoin(table schema.Tabler, on ...field.Expr) *permissionDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p permissionDo) Group(cols ...field.Expr) *permissionDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p permissionDo) Having(conds ...gen.Condition) *permissionDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p permissionDo) Limit(limit int) *permissionDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p permissionDo) Offset(offset int) *permissionDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p permissionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *permissionDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p permissionDo) Unscoped() *permissionDo {
	return p.withDO(p.DO.Unscoped())
}

func (p permissionDo) Create(values ...*entity.Permission) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p permissionDo) CreateInBatches(values []*entity.Permission, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p permissionDo) Save(values ...*entity.Permission) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p permissionDo) First() (*entity.Permission, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Permission), nil
	}
}

func (p permissionDo) Take() (*entity.Permission, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Permission), nil
	}
}

func (p permissionDo) Last() (*entity.Permission, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Permission), nil
	}
}

func (p permissionDo) Find() ([]*entity.Permission, error) {
	result, err := p.DO.Find()
	return result.([]*entity.Permission), err
}

func (p permissionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Permission, err error) {
	buf := make([]*entity.Permission, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p permissionDo) FindInBatches(result *[]*entity.Permission, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p permissionDo) Attrs(attrs ...field.AssignExpr) *permissionDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p permissionDo) Assign(attrs ...field.AssignExpr) *permissionDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p permissionDo) Joins(fields ...field.RelationField) *permissionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p permissionDo) Preload(fields ...field.RelationField) *permissionDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p permissionDo) FirstOrInit() (*entity.Permission, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Permission), nil
	}
}

func (p permissionDo) FirstOrCreate() (*entity.Permission, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Permission), nil
	}
}

func (p permissionDo) FindByPage(offset int, limit int) (result []*entity.Permission, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p permissionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p permissionDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p permissionDo) Delete(models ...*entity.Permission) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *permissionDo) withDO(do gen.Dao) *permissionDo {
	p.DO = *do.(*gen.DO)
	return p
}
