// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Admin/model/entity"
)

func newAddressCopy2(db *gorm.DB, opts ...gen.DOOption) addressCopy2 {
	_addressCopy2 := addressCopy2{}

	_addressCopy2.addressCopy2Do.UseDB(db, opts...)
	_addressCopy2.addressCopy2Do.UseModel(&entity.AddressCopy2{})

	tableName := _addressCopy2.addressCopy2Do.TableName()
	_addressCopy2.ALL = field.NewAsterisk(tableName)
	_addressCopy2.ID = field.NewInt64(tableName, "id")
	_addressCopy2.Trxaddr = field.NewString(tableName, "trxaddr")
	_addressCopy2.Ethaddr = field.NewString(tableName, "ethaddr")
	_addressCopy2.Trx = field.NewFloat64(tableName, "trx")
	_addressCopy2.UsdtTrc = field.NewFloat64(tableName, "usdt_trc")
	_addressCopy2.UsdtErc = field.NewFloat64(tableName, "usdt_erc")
	_addressCopy2.UsdtBsc = field.NewFloat64(tableName, "usdt_bsc")
	_addressCopy2.Eth = field.NewFloat64(tableName, "eth")
	_addressCopy2.Bnb = field.NewFloat64(tableName, "bnb")
	_addressCopy2.Num = field.NewInt32(tableName, "num")
	_addressCopy2.Ercnum = field.NewInt32(tableName, "ercnum")
	_addressCopy2.Bscnum = field.NewInt32(tableName, "bscnum")
	_addressCopy2.Lock = field.NewInt32(tableName, "lock")
	_addressCopy2.Leaseid = field.NewString(tableName, "leaseid")
	_addressCopy2.Key = field.NewString(tableName, "key")
	_addressCopy2.Locktime = field.NewInt64(tableName, "locktime")
	_addressCopy2.Userid = field.NewString(tableName, "userid")
	_addressCopy2.Callbackurl = field.NewString(tableName, "callbackurl")
	_addressCopy2.MerchantID = field.NewInt32(tableName, "merchant_id")
	_addressCopy2.MerchantName = field.NewString(tableName, "merchant_name")
	_addressCopy2.DelFlg = field.NewInt32(tableName, "del_flg")
	_addressCopy2.Trxlock = field.NewInt32(tableName, "trxlock")
	_addressCopy2.Ethlock = field.NewInt32(tableName, "ethlock")
	_addressCopy2.Bnblock = field.NewInt32(tableName, "bnblock")
	_addressCopy2.Ethhash = field.NewString(tableName, "ethhash")
	_addressCopy2.Usdthash = field.NewString(tableName, "usdthash")
	_addressCopy2.TrxStatus = field.NewInt32(tableName, "trx_status")
	_addressCopy2.EthStatus = field.NewInt32(tableName, "eth_status")
	_addressCopy2.BscStatus = field.NewInt32(tableName, "bsc_status")
	_addressCopy2.UpdateMoney = field.NewInt32(tableName, "update_money")
	_addressCopy2.Trcsid = field.NewInt32(tableName, "trcsid")
	_addressCopy2.Ercsid = field.NewInt32(tableName, "ercsid")
	_addressCopy2.Bscsid = field.NewInt32(tableName, "bscsid")

	_addressCopy2.fillFieldMap()

	return _addressCopy2
}

type addressCopy2 struct {
	addressCopy2Do

	ALL          field.Asterisk
	ID           field.Int64
	Trxaddr      field.String  // TRX地址
	Ethaddr      field.String  // ETH地址
	Trx          field.Float64 // TRX余额
	UsdtTrc      field.Float64 // 波场链USDT余额
	UsdtErc      field.Float64 // 以太坊链USDT余额
	UsdtBsc      field.Float64 // 币安链USDT余额
	Eth          field.Float64 // ETH余额
	Bnb          field.Float64 // BNB余额
	Num          field.Int32   // 笔数
	Ercnum       field.Int32   // erc笔数
	Bscnum       field.Int32   // bsc笔数
	Lock         field.Int32
	Leaseid      field.String
	Key          field.String // 私钥
	Locktime     field.Int64  // 锁定时间
	Userid       field.String // 商户绑定用户ID
	Callbackurl  field.String // 回调地址
	MerchantID   field.Int32  // 商户id
	MerchantName field.String // 商户名
	DelFlg       field.Int32
	Trxlock      field.Int32 // 用于trc归集 记录状态
	Ethlock      field.Int32 // 用于erc归集 记录状态
	Bnblock      field.Int32 // 用于bsc归集 记录状态
	Ethhash      field.String
	Usdthash     field.String
	TrxStatus    field.Int32
	EthStatus    field.Int32
	BscStatus    field.Int32
	UpdateMoney  field.Int32
	Trcsid       field.Int32
	Ercsid       field.Int32
	Bscsid       field.Int32

	fieldMap map[string]field.Expr
}

func (a addressCopy2) Table(newTableName string) *addressCopy2 {
	a.addressCopy2Do.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a addressCopy2) As(alias string) *addressCopy2 {
	a.addressCopy2Do.DO = *(a.addressCopy2Do.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *addressCopy2) updateTableName(table string) *addressCopy2 {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.Trxaddr = field.NewString(table, "trxaddr")
	a.Ethaddr = field.NewString(table, "ethaddr")
	a.Trx = field.NewFloat64(table, "trx")
	a.UsdtTrc = field.NewFloat64(table, "usdt_trc")
	a.UsdtErc = field.NewFloat64(table, "usdt_erc")
	a.UsdtBsc = field.NewFloat64(table, "usdt_bsc")
	a.Eth = field.NewFloat64(table, "eth")
	a.Bnb = field.NewFloat64(table, "bnb")
	a.Num = field.NewInt32(table, "num")
	a.Ercnum = field.NewInt32(table, "ercnum")
	a.Bscnum = field.NewInt32(table, "bscnum")
	a.Lock = field.NewInt32(table, "lock")
	a.Leaseid = field.NewString(table, "leaseid")
	a.Key = field.NewString(table, "key")
	a.Locktime = field.NewInt64(table, "locktime")
	a.Userid = field.NewString(table, "userid")
	a.Callbackurl = field.NewString(table, "callbackurl")
	a.MerchantID = field.NewInt32(table, "merchant_id")
	a.MerchantName = field.NewString(table, "merchant_name")
	a.DelFlg = field.NewInt32(table, "del_flg")
	a.Trxlock = field.NewInt32(table, "trxlock")
	a.Ethlock = field.NewInt32(table, "ethlock")
	a.Bnblock = field.NewInt32(table, "bnblock")
	a.Ethhash = field.NewString(table, "ethhash")
	a.Usdthash = field.NewString(table, "usdthash")
	a.TrxStatus = field.NewInt32(table, "trx_status")
	a.EthStatus = field.NewInt32(table, "eth_status")
	a.BscStatus = field.NewInt32(table, "bsc_status")
	a.UpdateMoney = field.NewInt32(table, "update_money")
	a.Trcsid = field.NewInt32(table, "trcsid")
	a.Ercsid = field.NewInt32(table, "ercsid")
	a.Bscsid = field.NewInt32(table, "bscsid")

	a.fillFieldMap()

	return a
}

func (a *addressCopy2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *addressCopy2) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 33)
	a.fieldMap["id"] = a.ID
	a.fieldMap["trxaddr"] = a.Trxaddr
	a.fieldMap["ethaddr"] = a.Ethaddr
	a.fieldMap["trx"] = a.Trx
	a.fieldMap["usdt_trc"] = a.UsdtTrc
	a.fieldMap["usdt_erc"] = a.UsdtErc
	a.fieldMap["usdt_bsc"] = a.UsdtBsc
	a.fieldMap["eth"] = a.Eth
	a.fieldMap["bnb"] = a.Bnb
	a.fieldMap["num"] = a.Num
	a.fieldMap["ercnum"] = a.Ercnum
	a.fieldMap["bscnum"] = a.Bscnum
	a.fieldMap["lock"] = a.Lock
	a.fieldMap["leaseid"] = a.Leaseid
	a.fieldMap["key"] = a.Key
	a.fieldMap["locktime"] = a.Locktime
	a.fieldMap["userid"] = a.Userid
	a.fieldMap["callbackurl"] = a.Callbackurl
	a.fieldMap["merchant_id"] = a.MerchantID
	a.fieldMap["merchant_name"] = a.MerchantName
	a.fieldMap["del_flg"] = a.DelFlg
	a.fieldMap["trxlock"] = a.Trxlock
	a.fieldMap["ethlock"] = a.Ethlock
	a.fieldMap["bnblock"] = a.Bnblock
	a.fieldMap["ethhash"] = a.Ethhash
	a.fieldMap["usdthash"] = a.Usdthash
	a.fieldMap["trx_status"] = a.TrxStatus
	a.fieldMap["eth_status"] = a.EthStatus
	a.fieldMap["bsc_status"] = a.BscStatus
	a.fieldMap["update_money"] = a.UpdateMoney
	a.fieldMap["trcsid"] = a.Trcsid
	a.fieldMap["ercsid"] = a.Ercsid
	a.fieldMap["bscsid"] = a.Bscsid
}

func (a addressCopy2) clone(db *gorm.DB) addressCopy2 {
	a.addressCopy2Do.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a addressCopy2) replaceDB(db *gorm.DB) addressCopy2 {
	a.addressCopy2Do.ReplaceDB(db)
	return a
}

type addressCopy2Do struct{ gen.DO }

func (a addressCopy2Do) Debug() *addressCopy2Do {
	return a.withDO(a.DO.Debug())
}

func (a addressCopy2Do) WithContext(ctx context.Context) *addressCopy2Do {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a addressCopy2Do) ReadDB() *addressCopy2Do {
	return a.Clauses(dbresolver.Read)
}

func (a addressCopy2Do) WriteDB() *addressCopy2Do {
	return a.Clauses(dbresolver.Write)
}

func (a addressCopy2Do) Session(config *gorm.Session) *addressCopy2Do {
	return a.withDO(a.DO.Session(config))
}

func (a addressCopy2Do) Clauses(conds ...clause.Expression) *addressCopy2Do {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a addressCopy2Do) Returning(value interface{}, columns ...string) *addressCopy2Do {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a addressCopy2Do) Not(conds ...gen.Condition) *addressCopy2Do {
	return a.withDO(a.DO.Not(conds...))
}

func (a addressCopy2Do) Or(conds ...gen.Condition) *addressCopy2Do {
	return a.withDO(a.DO.Or(conds...))
}

func (a addressCopy2Do) Select(conds ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Select(conds...))
}

func (a addressCopy2Do) Where(conds ...gen.Condition) *addressCopy2Do {
	return a.withDO(a.DO.Where(conds...))
}

func (a addressCopy2Do) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *addressCopy2Do {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a addressCopy2Do) Order(conds ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Order(conds...))
}

func (a addressCopy2Do) Distinct(cols ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a addressCopy2Do) Omit(cols ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Omit(cols...))
}

func (a addressCopy2Do) Join(table schema.Tabler, on ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Join(table, on...))
}

func (a addressCopy2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a addressCopy2Do) RightJoin(table schema.Tabler, on ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a addressCopy2Do) Group(cols ...field.Expr) *addressCopy2Do {
	return a.withDO(a.DO.Group(cols...))
}

func (a addressCopy2Do) Having(conds ...gen.Condition) *addressCopy2Do {
	return a.withDO(a.DO.Having(conds...))
}

func (a addressCopy2Do) Limit(limit int) *addressCopy2Do {
	return a.withDO(a.DO.Limit(limit))
}

func (a addressCopy2Do) Offset(offset int) *addressCopy2Do {
	return a.withDO(a.DO.Offset(offset))
}

func (a addressCopy2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *addressCopy2Do {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a addressCopy2Do) Unscoped() *addressCopy2Do {
	return a.withDO(a.DO.Unscoped())
}

func (a addressCopy2Do) Create(values ...*entity.AddressCopy2) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a addressCopy2Do) CreateInBatches(values []*entity.AddressCopy2, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a addressCopy2Do) Save(values ...*entity.AddressCopy2) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a addressCopy2Do) First() (*entity.AddressCopy2, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCopy2), nil
	}
}

func (a addressCopy2Do) Take() (*entity.AddressCopy2, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCopy2), nil
	}
}

func (a addressCopy2Do) Last() (*entity.AddressCopy2, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCopy2), nil
	}
}

func (a addressCopy2Do) Find() ([]*entity.AddressCopy2, error) {
	result, err := a.DO.Find()
	return result.([]*entity.AddressCopy2), err
}

func (a addressCopy2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.AddressCopy2, err error) {
	buf := make([]*entity.AddressCopy2, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a addressCopy2Do) FindInBatches(result *[]*entity.AddressCopy2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a addressCopy2Do) Attrs(attrs ...field.AssignExpr) *addressCopy2Do {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a addressCopy2Do) Assign(attrs ...field.AssignExpr) *addressCopy2Do {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a addressCopy2Do) Joins(fields ...field.RelationField) *addressCopy2Do {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a addressCopy2Do) Preload(fields ...field.RelationField) *addressCopy2Do {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a addressCopy2Do) FirstOrInit() (*entity.AddressCopy2, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCopy2), nil
	}
}

func (a addressCopy2Do) FirstOrCreate() (*entity.AddressCopy2, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AddressCopy2), nil
	}
}

func (a addressCopy2Do) FindByPage(offset int, limit int) (result []*entity.AddressCopy2, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a addressCopy2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a addressCopy2Do) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a addressCopy2Do) Delete(models ...*entity.AddressCopy2) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *addressCopy2Do) withDO(do gen.Dao) *addressCopy2Do {
	a.DO = *do.(*gen.DO)
	return a
}
