db:
  host: **************
  port: 3306
  username: root
  password: 123456
  database: yuanshen
log:
  path: "./logs"
  max_size: 500 #megabytes
  max_backups: 3
  max_age: 28 #days
jwt:
  secret: bJEdszpa7uPCyhU6H6EACRxngx3a2GW5
redis:
  url: **************:6379
  password: redis_tPxj6f
tg:
  token: 6318823381:AAEvPnggoZBNCSQJiaFqKXrEDIiTN3g8MoY
  tgid: 6038496780
eth:
  api: wss://mainnet.infura.io/ws/v3/********************************
  sun: 1000000000000000000
  token: ******************************************
  tokensun: 1000000
bsc:
  api: wss://rpc.ankr.com/bsc/ws/310c35562452e3195d61d117da6872e7e606024cbdc6c814910f459755acd4bd
  sun: 1000000000000000000
  token: ******************************************
  tokensun: 1000000000000000000
tron:
  api: https://api.trongrid.io
  appkey: 3a96e5b3-b80e-46c1-b076-8031c90b414f
  sun: 1000000
  token: TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t
addr:
  trc: TBq2FPaPpxoLPbzNzth8LsGb71K3igR1k5
  erc: ******************************************
  key: 9dd6c5ab68c2dacad1e621cad2167ace27e7812a3ab27fbcd9e182d88eac4cfbf57fa67b5a4a9af09c68f82b1689f66a0a4ae48cc4fc0f16b50bf1bd286d71a991009f2aed466c773d7fe5f49a73985842c58bb041b4e7859d23aeb430d8345963d495783d42a4d1
  testtrc: TV2TQxnNLJjf6s5m3FXK46RaVnb6EVBkx7
leaseapi:
  url: http://*************:3000
  key: aac25838-1422-11ee-af0e-fa163e58e4ed
tgtrxaddress:
  address: TXt9meQitUd9xXosNy1rVggZkfhizvz6pv
  num: 3