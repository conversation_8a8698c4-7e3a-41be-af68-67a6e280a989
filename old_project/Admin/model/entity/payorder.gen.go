// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNamePayorder = "payorder"

// Payorder mapped from table <payorder>
type Payorder struct {
	ID             int64    `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	MerchantID     *string  `gorm:"column:merchant_id;type:varchar(11);comment:商户id" json:"merchant_id"`
	Appkey         *string  `gorm:"column:appkey;type:varchar(255);comment:商户密钥" json:"appkey"`
	Transactionid  *string  `gorm:"column:transactionid;type:varchar(255)" json:"transactionid"`
	Orderid        *string  `gorm:"column:orderid;type:varchar(255);comment:订单号" json:"orderid"`
	Rorderid       *string  `gorm:"column:rorderid;type:varchar(255);comment:商户订单id" json:"rorderid"`
	Addr           *string  `gorm:"column:addr;type:varchar(255);comment:收款地址" json:"addr"`
	Money          *string  `gorm:"column:money;type:varchar(255);comment:金额" json:"money"`
	Coin           *string  `gorm:"column:coin;type:varchar(255);comment:币种" json:"coin"`
	TranFee        *float64 `gorm:"column:tran_fee;type:decimal(11,2);comment:预估矿工费" json:"tran_fee"`
	RealTranFee    *float64 `gorm:"column:real_tran_fee;type:decimal(11,2);comment:实际花费矿工费" json:"real_tran_fee"`
	Status         *int32   `gorm:"column:status;type:int;comment:0:创建订单 1:超时 2:转账成功 3:回调成功 4:主动成功 5:转账失败" json:"status"`
	Callbackurl    *string  `gorm:"column:callbackurl;type:varchar(255);comment:回调地址" json:"callbackurl"`
	Callbacknum    *int32   `gorm:"column:callbacknum;type:int;comment:回调次数" json:"callbacknum"`
	Callbackresult *string  `gorm:"column:callbackresult;type:text;comment:回调返回信息" json:"callbackresult"`
	MerchantName   *string  `gorm:"column:merchant_name;type:varchar(191);comment:商户名" json:"merchant_name"`
	Ctime          *int32   `gorm:"column:ctime;type:int" json:"ctime"`
	Etime          *int32   `gorm:"column:etime;type:int" json:"etime"`
	Returnurl      *string  `gorm:"column:returnurl;type:varchar(255);comment:跳转地址" json:"returnurl"`
	Rmb            *string  `gorm:"column:rmb;type:varchar(255)" json:"rmb"`
	Rate           *string  `gorm:"column:rate;type:varchar(255);comment:汇率" json:"rate"`
	Chain          *string  `gorm:"column:chain;type:varchar(191);comment:链" json:"chain"`
	Lease          *int32   `gorm:"column:lease;type:int;comment:能量是否到账 0 未到账 1 已到账" json:"lease"`
	Leaseid        *string  `gorm:"column:leaseid;type:varchar(255);comment:租赁订单号" json:"leaseid"`
}

// TableName Payorder's table name
func (*Payorder) TableName() string {
	return TableNamePayorder
}
