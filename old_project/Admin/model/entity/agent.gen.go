// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameAgent = "agent"

// Agent mapped from table <agent>
type Agent struct {
	ID           int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt    *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt    *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_agent_deleted_at,priority:1" json:"deleted_at"`
	Username     *string        `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Password     *string        `gorm:"column:password;type:varchar(255);comment:密码" json:"password"`
	Fee          *float64       `gorm:"column:fee;type:decimal(11,2);comment:费率" json:"fee"`
	Ethfee       *float64       `gorm:"column:ethfee;type:decimal(11,2);comment:eth费率" json:"ethfee"`
	Money        *float64       `gorm:"column:money;type:decimal(30,6);comment:余额" json:"money"`
	LockMoney    *float64       `gorm:"column:lock_money;type:decimal(30,6);comment:锁定余额" json:"lock_money"`
	ErclockMoney *float64       `gorm:"column:erclock_money;type:decimal(30,6);comment:ERC锁定余额" json:"erclock_money"`
	BsclockMoney *float64       `gorm:"column:bsclock_money;type:decimal(30,6);comment:BSC锁定余额" json:"bsclock_money"`
	Ercmoney     *float64       `gorm:"column:ercmoney;type:decimal(30,6);comment:ERC余额" json:"ercmoney"`
	Bscmoney     *float64       `gorm:"column:bscmoney;type:decimal(30,6);comment:BSC余额" json:"bscmoney"`
	Bscfee       *float64       `gorm:"column:bscfee;type:decimal(11,2);comment:bsc费率" json:"bscfee"`
	Secret       *string        `gorm:"column:secret;type:varchar(255);comment:Google密钥" json:"secret"`
}

// TableName Agent's table name
func (*Agent) TableName() string {
	return TableNameAgent
}
