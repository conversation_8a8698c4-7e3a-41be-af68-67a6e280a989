// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameAddress = "address"

// Address mapped from table <address>
type Address struct {
	ID           int64    `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	Trxaddr      *string  `gorm:"column:trxaddr;type:varchar(255);comment:TRX地址" json:"trxaddr"`
	Ethaddr      *string  `gorm:"column:ethaddr;type:varchar(255);comment:ETH地址" json:"ethaddr"`
	Trx          *float64 `gorm:"column:trx;type:decimal(20,5);comment:TRX余额" json:"trx"`
	UsdtTrc      *float64 `gorm:"column:usdt_trc;type:decimal(20,5);comment:波场链USDT余额" json:"usdt_trc"`
	UsdtErc      *float64 `gorm:"column:usdt_erc;type:decimal(20,5);comment:以太坊链USDT余额" json:"usdt_erc"`
	UsdtBsc      *float64 `gorm:"column:usdt_bsc;type:decimal(20,5);comment:币安链USDT余额" json:"usdt_bsc"`
	Eth          *float64 `gorm:"column:eth;type:decimal(20,5);comment:ETH余额" json:"eth"`
	Bnb          *float64 `gorm:"column:bnb;type:decimal(20,5);comment:BNB余额" json:"bnb"`
	Num          *int32   `gorm:"column:num;type:smallint;comment:笔数" json:"num"`
	Ercnum       *int32   `gorm:"column:ercnum;type:smallint;comment:erc笔数" json:"ercnum"`
	Bscnum       *int32   `gorm:"column:bscnum;type:smallint;comment:bsc笔数" json:"bscnum"`
	Lock         *int32   `gorm:"column:lock;type:smallint" json:"lock"`
	Leaseid      *string  `gorm:"column:leaseid;type:varchar(255)" json:"leaseid"`
	Key          *string  `gorm:"column:key;type:varchar(255);comment:私钥" json:"key"`
	Locktime     *int64   `gorm:"column:locktime;type:bigint;comment:锁定时间" json:"locktime"`
	Userid       *string  `gorm:"column:userid;type:varchar(255);comment:商户绑定用户ID" json:"userid"`
	Callbackurl  *string  `gorm:"column:callbackurl;type:varchar(255);comment:回调地址" json:"callbackurl"`
	MerchantID   *int32   `gorm:"column:merchant_id;type:smallint;comment:商户id" json:"merchant_id"`
	MerchantName *string  `gorm:"column:merchant_name;type:varchar(255);comment:商户名" json:"merchant_name"`
	DelFlg       *int32   `gorm:"column:del_flg;type:int" json:"del_flg"`
	Trxlock      *int32   `gorm:"column:trxlock;type:smallint;comment:用于trc归集 记录状态" json:"trxlock"`
	Ethlock      *int32   `gorm:"column:ethlock;type:smallint;comment:用于erc归集 记录状态" json:"ethlock"`
	Bnblock      *int32   `gorm:"column:bnblock;type:smallint;comment:用于bsc归集 记录状态" json:"bnblock"`
	Ethhash      *string  `gorm:"column:ethhash;type:varchar(255)" json:"ethhash"`
	Usdthash     *string  `gorm:"column:usdthash;type:varchar(255)" json:"usdthash"`
	TrxStatus    *int32   `gorm:"column:trx_status;type:int" json:"trx_status"`
	EthStatus    int32    `gorm:"column:eth_status;type:int;not null;default:1" json:"eth_status"`
	BscStatus    *int32   `gorm:"column:bsc_status;type:int;default:1" json:"bsc_status"`
	UpdateMoney  *int32   `gorm:"column:update_money;type:int" json:"update_money"`
	Trcsid       int32    `gorm:"column:trcsid;type:int;not null" json:"trcsid"`
	Ercsid       int32    `gorm:"column:ercsid;type:int;not null" json:"ercsid"`
	Bscsid       int32    `gorm:"column:bscsid;type:int;not null" json:"bscsid"`
}

// TableName Address's table name
func (*Address) TableName() string {
	return TableNameAddress
}
