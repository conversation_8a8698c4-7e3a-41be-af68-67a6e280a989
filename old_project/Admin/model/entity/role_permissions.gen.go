// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameRolePermission = "role_permissions"

// RolePermission mapped from table <role_permissions>
type RolePermission struct {
	ID            int32  `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Roleid        *int32 `gorm:"column:roleid;type:int;comment:角色表id" json:"roleid"`
	Permissionsid *int32 `gorm:"column:permissionsid;type:int;comment:权限表id" json:"permissionsid"`
	Ctime         *int32 `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg        *int32 `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName RolePermission's table name
func (*RolePermission) TableName() string {
	return TableNameRolePermission
}
