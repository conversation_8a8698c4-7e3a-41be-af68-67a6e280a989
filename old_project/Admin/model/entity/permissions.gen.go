// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNamePermission = "permissions"

// Permission mapped from table <permissions>
type Permission struct {
	ID          int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Name        *string `gorm:"column:name;type:varchar(255);comment:名称" json:"name"`
	Description *string `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`
	Route       *string `gorm:"column:route;type:varchar(255);comment:路由" json:"route"`
	Method      *string `gorm:"column:method;type:varchar(255);comment:GET还是POST" json:"method"`
	Ctime       *int32  `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg      *int32  `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName Permission's table name
func (*Permission) TableName() string {
	return TableNamePermission
}
