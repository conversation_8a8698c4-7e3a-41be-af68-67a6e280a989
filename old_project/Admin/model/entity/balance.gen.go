// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameBalance = "balance"

// Balance mapped from table <balance>
type Balance struct {
	ID           int32    `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	MerchantID   *int32   `gorm:"column:merchant_id;type:int" json:"merchant_id"`
	MerchantName *string  `gorm:"column:merchant_name;type:varchar(255)" json:"merchant_name"`
	Trc          *float64 `gorm:"column:trc;type:decimal(11,5)" json:"trc"`
	Erc          *float64 `gorm:"column:erc;type:decimal(11,5)" json:"erc"`
	Bsc          *float64 `gorm:"column:bsc;type:decimal(11,5)" json:"bsc"`
	Ctime        *int32   `gorm:"column:ctime;type:int" json:"ctime"`
}

// TableName Balance's table name
func (*Balance) TableName() string {
	return TableNameBalance
}
