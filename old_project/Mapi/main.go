package main

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/internal/gofiber"
	"Mapi/internal/lib"
	"Mapi/internal/logger"
	"Mapi/internal/queue"
	"Mapi/internal/transfer"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

func main() {
	fx.New(
		fx.Provide(
			logger.NewLogger,
			config.NewConfig,
			dal.NewGormDB,
			gofiber.NewFiberApp,
			lib.NewWsServerUrl,
			queue.StartQueue,
		),
		fx.Invoke(
			func(*gorm.DB) {},
			lib.WsServer,
			gofiber.RunFiberServer,
			queue.ExecuteTasks,
			transfer.InitEthNet,
			transfer.InitTron,
			//transfer.InitBscNet,
			transfer.Pool,
			lib.UsdtWsServer,
		),
	).Run()
}
