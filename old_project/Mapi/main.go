package main

import (
	"Mapi/config"
	"Mapi/dal"
	"Mapi/internal/gofiber"
	"Mapi/internal/logger"
	// "Mapi/internal/queue" // 注释掉 Redis 队列包
	"Mapi/internal/transfer"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

func main() {
	fx.New(
		fx.Provide(
			logger.NewLogger,
			config.NewConfig,
			dal.NewGormDB,
			gofiber.NewFiberApp,
			// lib.NewWsServerUrl, // 注释掉 WebSocket URL 提供者
			// queue.StartQueue, // 注释掉 Redis 队列启动
		),
		fx.Invoke(
			func(*gorm.DB) {},
			// lib.WsServer, // 注释掉 WebSocket 服务器
			gofiber.RunFiberServer,
			// queue.ExecuteTasks, // 注释掉 Redis 队列任务执行
			transfer.InitEthNet,
			transfer.InitTron,
			//transfer.InitBscNet,
			transfer.Pool,
			// lib.UsdtWsServer, // 注释掉 USDT WebSocket 服务器
		),
	).Run()
}
