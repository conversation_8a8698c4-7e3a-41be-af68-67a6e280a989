// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameAgentTran = "agent_trans"

// AgentTran mapped from table <agent_trans>
type AgentTran struct {
	ID            int32    `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	UserID        *int32   `gorm:"column:user_id;type:int;comment:用户id" json:"user_id"`
	Type          *int32   `gorm:"column:type;type:int;comment:1代理提成(TRC) 2代理提成(ERC) 3代理提成(BSC) 4提现支出(TRC) 5提现支出(ERC) 6提现支出(BSC)" json:"type"`
	Money         *float64 `gorm:"column:money;type:decimal(20,5);comment:金额" json:"money"`
	Orderid       *string  `gorm:"column:orderid;type:varchar(255);comment:平台订单id" json:"orderid"`
	Rorderid      *string  `gorm:"column:rorderid;type:varchar(255);comment:商户订单id" json:"rorderid"`
	Transactionid *string  `gorm:"column:transactionid;type:varchar(255);comment:hash" json:"transactionid"`
	Username      *string  `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Remark        *string  `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	Ctime         *int32   `gorm:"column:ctime;type:int" json:"ctime"`
}

// TableName AgentTran's table name
func (*AgentTran) TableName() string {
	return TableNameAgentTran
}
