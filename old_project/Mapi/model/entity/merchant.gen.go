// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNameMerchant = "merchant"

// Merchant mapped from table <merchant>
type Merchant struct {
	ID                 int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreatedAt          *time.Time     `gorm:"column:created_at;type:datetime(3)" json:"created_at"`
	UpdatedAt          *time.Time     `gorm:"column:updated_at;type:datetime(3)" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_sys_merchant_deleted_at,priority:1" json:"deleted_at"`
	Username           *string        `gorm:"column:username;type:varchar(50);comment:用户名" json:"username"`
	Password           *string        `gorm:"column:password;type:varchar(255);comment:密码" json:"password"`
	Status             *int32         `gorm:"column:status;type:int;comment:状态 启用0 禁用1" json:"status"`
	Fee                *float64       `gorm:"column:fee;type:decimal(11,2);comment:费率" json:"fee"`
	Ethfee             *float64       `gorm:"column:ethfee;type:decimal(11,2);comment:eth费率" json:"ethfee"`
	Bscfee             *float64       `gorm:"column:bscfee;type:decimal(11,2);comment:bsc费率" json:"bscfee"`
	Money              *float64       `gorm:"column:money;type:decimal(11,5);comment:TRC余额" json:"money"`
	LockMoney          *float64       `gorm:"column:lock_money;type:decimal(11,5);comment:TRC锁定余额" json:"lock_money"`
	FeeMoney           *float64       `gorm:"column:fee_money;type:decimal(11,2);comment:TRC手续费余额(TRX)" json:"fee_money"`
	EthFeemoney        *float64       `gorm:"column:eth_feemoney;type:decimal(11,5);comment:ERC手续费余额(ETH)" json:"eth_feemoney"`
	ErcLockmoney       *float64       `gorm:"column:erc_lockmoney;type:decimal(11,5);comment:ERC锁定余额" json:"erc_lockmoney"`
	ErcMoney           *float64       `gorm:"column:erc_money;type:decimal(11,5);comment:ERC余额" json:"erc_money"`
	BscMoney           *float64       `gorm:"column:bsc_money;type:decimal(11,5);comment:BSC余额" json:"bsc_money"`
	BscLockmoney       *float64       `gorm:"column:bsc_lockmoney;type:decimal(11,5);comment:BSC锁定余额" json:"bsc_lockmoney"`
	BscFeemoney        *float64       `gorm:"column:bsc_feemoney;type:decimal(11,5);comment:BSC手续费余额(BNB)" json:"bsc_feemoney"`
	Secret             *string        `gorm:"column:secret;type:varchar(255);comment:Google密钥" json:"secret"`
	Paysecret          *string        `gorm:"column:paysecret;type:varchar(255);comment:下发Google密钥" json:"paysecret"`
	AgentID            *int32         `gorm:"column:agent_id;type:int;comment:所属代理id" json:"agent_id"`
	AgentName          *string        `gorm:"column:agent_name;type:varchar(255);comment:所属代理名" json:"agent_name"`
	EthAgentFee        *float64       `gorm:"column:eth_agent_fee;type:decimal(11,2);comment:eth代理费率" json:"eth_agent_fee"`
	Appkey             *string        `gorm:"column:appkey;type:varchar(255);comment:密钥" json:"appkey"`
	Token              *string        `gorm:"column:token;type:varchar(255);comment:token" json:"token"`
	Credits            *float64       `gorm:"column:credits;type:decimal(11,2);comment:信用额度(可透支手续费)" json:"credits"`
	Rate               *float64       `gorm:"column:rate;type:decimal(11,2);comment:固定汇率" json:"rate"`
	Ratetype           *string        `gorm:"column:ratetype;type:varchar(255);comment:汇率类型 1固定 2浮动" json:"ratetype"`
	Floatratetype      *string        `gorm:"column:floatratetype;type:varchar(255);comment:浮动汇率差值类型 1百分比 2固定数值" json:"floatratetype"`
	Floatratedvalue    *string        `gorm:"column:floatratedvalue;type:varchar(255);comment:浮动汇率差值" json:"floatratedvalue"`
	AgentFee           *float64       `gorm:"column:agent_fee;type:decimal(11,2);comment:代理费率" json:"agent_fee"`
	IsFloatdown        *string        `gorm:"column:is_floatdown;type:varchar(11);comment:是否开启下浮" json:"is_floatdown"`
	Largeordertimeout  *string        `gorm:"column:largeordertimeout;type:varchar(255);comment:大额订单超时时间" json:"largeordertimeout"`
	Smallordertimeout  *string        `gorm:"column:smallordertimeout;type:varchar(255);comment:小额订单超时时间" json:"smallordertimeout"`
	Largeorderlocktime *string        `gorm:"column:largeorderlocktime;type:varchar(255);comment:大额订单trx锁定时间" json:"largeorderlocktime"`
	Smallorderlocktime *string        `gorm:"column:smallorderlocktime;type:varchar(255);comment:小额订单trx锁定时间" json:"smallorderlocktime"`
	Largeorderamount   *string        `gorm:"column:largeorderamount;type:varchar(255);comment:大额订单金额(>)" json:"largeorderamount"`
	AddressID          *int32         `gorm:"column:address_id;type:int;comment:地址ID" json:"address_id"`
	TrcStatus          *int32         `gorm:"column:trc_status;type:int;comment:TRC归集状态 0未执行归集 1等待归集 2归集执行中" json:"trc_status"`
	ErcStatus          *int32         `gorm:"column:erc_status;type:int;comment:ERC归集状态 0未执行归集 1等待归集 2归集执行中" json:"erc_status"`
	BscStatus          *int32         `gorm:"column:bsc_status;type:int;comment:BSC归集状态 0未执行归集 1等待归集 2归集执行中" json:"bsc_status"`
	GatherType         *int32         `gorm:"column:gather_type;type:int;comment:归集类型 1自动归集 2下发时归集" json:"gather_type"`
	Trcneedmoney       *string        `gorm:"column:trcneedmoney;type:varchar(255);comment:归集用" json:"trcneedmoney"`
	Ercneedmoney       *string        `gorm:"column:ercneedmoney;type:varchar(255);comment:归集用" json:"ercneedmoney"`
	Bscneedmoney       *string        `gorm:"column:bscneedmoney;type:varchar(255);comment:归集用" json:"bscneedmoney"`
	IsLimitAmount      *int32         `gorm:"column:is_limit_amount;type:int;comment:下发是否限制最低订单金额" json:"is_limit_amount"`
	IsTieredRate       *int32         `gorm:"column:is_tiered_rate;type:int;comment:是否是阶梯汇率 0否 1是" json:"is_tiered_rate"`
	CommissionPrice    *float64       `gorm:"column:commission_price;type:decimal(10,2);default:0.00;comment:商户固定按笔数的手续费" json:"commission_price"`
}

// TableName Merchant's table name
func (*Merchant) TableName() string {
	return TableNameMerchant
}
