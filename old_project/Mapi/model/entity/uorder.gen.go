// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameUorder = "uorder"

// Uorder mapped from table <uorder>
type Uorder struct {
	ID     int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Addrid *int32  `gorm:"column:addrid;type:int" json:"addrid"`
	Toaddr *string `gorm:"column:toaddr;type:varchar(255)" json:"toaddr"`
	Money  *string `gorm:"column:money;type:varchar(255)" json:"money"`
	Chain  *string `gorm:"column:chain;type:varchar(255)" json:"chain"`
	Status *int32  `gorm:"column:status;type:int" json:"status"`
	Ctime  *int32  `gorm:"column:ctime;type:int" json:"ctime"`
}

// TableName Uorder's table name
func (*Uorder) TableName() string {
	return TableNameUorder
}
