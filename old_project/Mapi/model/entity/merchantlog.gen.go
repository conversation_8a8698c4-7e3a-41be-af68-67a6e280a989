// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"
)

const TableNameMerchantlog = "merchantlog"

// Merchantlog mapped from table <merchantlog>
type Merchantlog struct {
	ID           int32      `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	MerchantID   *int32     `gorm:"column:merchant_id;type:int;comment:商户id" json:"merchant_id"`
	MerchantName *string    `gorm:"column:merchant_name;type:varchar(255);comment:商户名" json:"merchant_name"`
	IP           *string    `gorm:"column:ip;type:varchar(255);comment:ip" json:"ip"`
	Type         *int32     `gorm:"column:type;type:int;comment:操作类型" json:"type"`
	Content      *string    `gorm:"column:content;type:varchar(255);comment:关联数据" json:"content"`
	Ctime        *int32     `gorm:"column:ctime;type:int" json:"ctime"`
	CreatedAt    *time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
}

// TableName Merchantlog's table name
func (*Merchantlog) TableName() string {
	return TableNameMerchantlog
}
