// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

const TableNameAdminslog = "adminslog"

// Adminslog mapped from table <adminslog>
type Adminslog struct {
	ID       int32   `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	Adminid  *int32  `gorm:"column:adminid;type:int" json:"adminid"`
	Username *string `gorm:"column:username;type:varchar(255)" json:"username"`
	Action   *string `gorm:"column:action;type:varchar(255);comment:操作" json:"action"`
	Route    *string `gorm:"column:route;type:varchar(255);comment:路由" json:"route"`
	Method   *string `gorm:"column:method;type:varchar(255);comment:GET POST" json:"method"`
	Content  *string `gorm:"column:content;type:text;comment:请求数据" json:"content"`
	IP       *string `gorm:"column:ip;type:varchar(255);comment:ip地址" json:"ip"`
	Ctime    *int32  `gorm:"column:ctime;type:int" json:"ctime"`
	DelFlg   *int32  `gorm:"column:del_flg;type:int" json:"del_flg"`
}

// TableName Adminslog's table name
func (*Adminslog) TableName() string {
	return TableNameAdminslog
}
