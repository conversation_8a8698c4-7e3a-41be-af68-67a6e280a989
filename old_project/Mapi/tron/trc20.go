package tron

import (
	"Mapi/api"
	"crypto/tls"
	"encoding/json"
	_ "fmt"
	"io"
	"math/big"
	"net/http"
	"time"
)

const (
	TRC20_ABI = `[{"constant":true,"inputs":[],"name":"name","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"name":"_spender","type":"address"},{"name":"_value","type":"uint256"}],"name":"approve","outputs":[{"name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"totalSupply","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"name":"_from","type":"address"},{"name":"_to","type":"address"},{"name":"_value","type":"uint256"}],"name":"transferFrom","outputs":[{"name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[{"name":"_owner","type":"address"}],"name":"balanceOf","outputs":[{"name":"balance","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":true,"inputs":[],"name":"symbol","outputs":[{"name":"","type":"string"}],"payable":false,"stateMutability":"view","type":"function"},{"constant":false,"inputs":[{"name":"_to","type":"address"},{"name":"_value","type":"uint256"}],"name":"transfer","outputs":[{"name":"","type":"bool"}],"payable":false,"stateMutability":"nonpayable","type":"function"},{"constant":true,"inputs":[{"name":"_owner","type":"address"},{"name":"_spender","type":"address"}],"name":"allowance","outputs":[{"name":"","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"},{"payable":true,"stateMutability":"payable","type":"fallback"},{"anonymous":false,"inputs":[{"indexed":true,"name":"owner","type":"address"},{"indexed":true,"name":"spender","type":"address"},{"indexed":false,"name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"name":"from","type":"address"},{"indexed":true,"name":"to","type":"address"},{"indexed":false,"name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]`
)

type Trc20 struct {
	*Contract
}

// 新增
func (t *Trc20) GetUsdtTransactionsTo(address, fingerprint string) (api.GetUsdtTransactions, error) {
	var r api.GetUsdtTransactions
	token := t.contractAddress
	url := t.tronApi.fullNode.url
	url += "/v1/accounts/" + address + "/transactions/trc20?only_to=true&limit=200&contract_address=" + token
	if fingerprint != "" {
		url += "&fingerprint=" + fingerprint
	}
	data := Get(url)
	//fmt.Println(data)
	err := json.Unmarshal([]byte(data), &r)
	return r, err
}
func Get(url string) string { //Get请求
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: transport,
	}
	reqest, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return ""
	}
	//增加header选项
	reqest.Header.Add("Accept", "application/json") //处理返回结果
	response, err := client.Do(reqest)
	if err != nil {
		return ""
	}
	defer response.Body.Close()
	r, e := io.ReadAll(response.Body)
	if e != nil {
		return ""
	}
	return string(r)
}

// 新增结束
func NewTrc20(tronApi *TronApi, credential *Credential, contractAddress string) (*Trc20, error) {
	contract, err := NewContract(tronApi, credential, []byte(TRC20_ABI))
	if err != nil {
		return nil, err
	}
	contract.At(contractAddress)
	return &Trc20{Contract: contract}, nil
}

func (t *Trc20) Transfer(to string, value *big.Int) (*TransactionResult, error) {
	_to, err := Base58ToEthAddress(to)
	if err != nil {
		return nil, err
	}
	return t.Transact("transfer", _to, value)
}

func (t *Trc20) TransferFrom(from, to string, value *big.Int) (*TransactionResult, error) {
	_from, err := Base58ToEthAddress(from)
	if err != nil {
		return nil, err
	}
	_to, err := Base58ToEthAddress(to)
	if err != nil {
		return nil, err
	}
	return t.Transact("transferFrom", _from, _to, value)
}

func (t *Trc20) Approve(spender string, value *big.Int) (*TransactionResult, error) {
	_spender, err := Base58ToEthAddress(spender)
	if err != nil {
		return nil, err
	}
	return t.Transact("approve", _spender)
}

func (t *Trc20) Allowance(owner, spender string) (*big.Int, error) {
	_owner, err := Base58ToEthAddress(owner)
	if err != nil {
		return nil, err
	}
	_spender, err := Base58ToEthAddress(spender)
	if err != nil {
		return nil, err
	}
	var ret = new(*big.Int)
	err = t.Call(ret, "allowance", _owner, _spender)
	return *ret, err
}

func (t *Trc20) BalanceOf(owner string) (*big.Int, error) {
	_owner, err := Base58ToEthAddress(owner)
	if err != nil {
		return nil, err
	}
	var ret = new(*big.Int)
	err = t.Call(ret, "balanceOf", _owner)
	return *ret, err
}

func (t *Trc20) TotalSupply() (*big.Int, error) {
	var ret = new(*big.Int)
	err := t.Call(ret, "totalSupply")
	return *ret, err
}

func (t *Trc20) Name() (string, error) {
	var ret = new(string)
	err := t.Call(ret, "name")
	return *ret, err
}

func (t *Trc20) Symbol() (string, error) {
	var ret = new(string)
	err := t.Call(ret, "symbol")
	return *ret, err
}

func (t *Trc20) Decimals() (uint8, error) {
	var ret = new(uint8)
	err := t.Call(ret, "decimals")
	return *ret, err
}
