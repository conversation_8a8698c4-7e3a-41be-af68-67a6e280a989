package api;

type CreateTransactionRequest struct{
  ToAddress string `json:"to_address"`
  OwnerAddress string `json:"owner_address"`
  Amount int64 `json:"amount"`
  PermissionId int32 `json:"permission_id"`
  Visible bool `json:"visible"`
}

type TransferTransaction struct{
  Visible bool `json:"visible"`
  TxId string `json:"txID"`
  RawData RawData01 `json:"raw_data"`
  RawDataHex string `json:"raw_data_hex"`
  Signature []string `json:"signature"`
}

type RawData01 struct{
  Contract []Contract01 `json:"contract"`
  RefBlockBytes string `json:"ref_block_bytes"`
  RefBlockHash string `json:"ref_block_hash"`
  Expiration int64 `json:"expiration"`
  TimeStamp int64 `json:"timestamp"`
}

type Contract01 struct{
  Parameter Parameter01 `json:"parameter"`
  Type string `json:"type"`
}

type Parameter01 struct{
  Value Value01 `json:"value"`
  TypeUrl string `json:"type_url"`
}

type Value01 struct{
  Amount int64 `json:"amount"`
  ToAddress string `json:"to_address"`
  OwnerAddress string `json:"owner_address"`
}

func NewCreateTransactionRequest(to string, amount int64, owner string) *CreateTransactionRequest {
  return &CreateTransactionRequest{
    ToAddress: to,
    OwnerAddress: owner,
    Amount: amount,
    PermissionId: 0,
    Visible: false,
  }
}