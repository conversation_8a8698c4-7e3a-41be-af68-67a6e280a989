package api

type ContractEventFilter struct{
  Since int64 `url:"since"`
  Sort string `url:"sort"`
}

type ContractEvent struct{
  CallerContractAddress string `json:"caller_contract_address"`
  TransactionId string `json:"transaction_id"`
  Result map[string]interface{} `json:"result"`
  ResultType map[string]string `json:"result_type"`
  BlockTimestamp int64 `json:"block_timestamp"`
  BlockNumber int64 `json:"block_number"`
  EventName string `json:"event_name"`
  ContractAddress string `json:"contract_address"`
  EventIndex int64 `json:"event_index"`
}
