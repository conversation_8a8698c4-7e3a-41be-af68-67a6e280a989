// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newUorder(db *gorm.DB, opts ...gen.DOOption) uorder {
	_uorder := uorder{}

	_uorder.uorderDo.UseDB(db, opts...)
	_uorder.uorderDo.UseModel(&entity.Uorder{})

	tableName := _uorder.uorderDo.TableName()
	_uorder.ALL = field.NewAsterisk(tableName)
	_uorder.ID = field.NewInt32(tableName, "id")
	_uorder.Addrid = field.NewInt32(tableName, "addrid")
	_uorder.Toaddr = field.NewString(tableName, "toaddr")
	_uorder.Money = field.NewString(tableName, "money")
	_uorder.Chain = field.NewString(tableName, "chain")
	_uorder.Status = field.NewInt32(tableName, "status")
	_uorder.Ctime = field.NewInt32(tableName, "ctime")

	_uorder.fillFieldMap()

	return _uorder
}

type uorder struct {
	uorderDo

	ALL    field.Asterisk
	ID     field.Int32
	Addrid field.Int32
	Toaddr field.String
	Money  field.String
	Chain  field.String
	Status field.Int32
	Ctime  field.Int32

	fieldMap map[string]field.Expr
}

func (u uorder) Table(newTableName string) *uorder {
	u.uorderDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u uorder) As(alias string) *uorder {
	u.uorderDo.DO = *(u.uorderDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *uorder) updateTableName(table string) *uorder {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.Addrid = field.NewInt32(table, "addrid")
	u.Toaddr = field.NewString(table, "toaddr")
	u.Money = field.NewString(table, "money")
	u.Chain = field.NewString(table, "chain")
	u.Status = field.NewInt32(table, "status")
	u.Ctime = field.NewInt32(table, "ctime")

	u.fillFieldMap()

	return u
}

func (u *uorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *uorder) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 7)
	u.fieldMap["id"] = u.ID
	u.fieldMap["addrid"] = u.Addrid
	u.fieldMap["toaddr"] = u.Toaddr
	u.fieldMap["money"] = u.Money
	u.fieldMap["chain"] = u.Chain
	u.fieldMap["status"] = u.Status
	u.fieldMap["ctime"] = u.Ctime
}

func (u uorder) clone(db *gorm.DB) uorder {
	u.uorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u uorder) replaceDB(db *gorm.DB) uorder {
	u.uorderDo.ReplaceDB(db)
	return u
}

type uorderDo struct{ gen.DO }

func (u uorderDo) Debug() *uorderDo {
	return u.withDO(u.DO.Debug())
}

func (u uorderDo) WithContext(ctx context.Context) *uorderDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u uorderDo) ReadDB() *uorderDo {
	return u.Clauses(dbresolver.Read)
}

func (u uorderDo) WriteDB() *uorderDo {
	return u.Clauses(dbresolver.Write)
}

func (u uorderDo) Session(config *gorm.Session) *uorderDo {
	return u.withDO(u.DO.Session(config))
}

func (u uorderDo) Clauses(conds ...clause.Expression) *uorderDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u uorderDo) Returning(value interface{}, columns ...string) *uorderDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u uorderDo) Not(conds ...gen.Condition) *uorderDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u uorderDo) Or(conds ...gen.Condition) *uorderDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u uorderDo) Select(conds ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u uorderDo) Where(conds ...gen.Condition) *uorderDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u uorderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *uorderDo {
	return u.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (u uorderDo) Order(conds ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u uorderDo) Distinct(cols ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u uorderDo) Omit(cols ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u uorderDo) Join(table schema.Tabler, on ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u uorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *uorderDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u uorderDo) RightJoin(table schema.Tabler, on ...field.Expr) *uorderDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u uorderDo) Group(cols ...field.Expr) *uorderDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u uorderDo) Having(conds ...gen.Condition) *uorderDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u uorderDo) Limit(limit int) *uorderDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u uorderDo) Offset(offset int) *uorderDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u uorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *uorderDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u uorderDo) Unscoped() *uorderDo {
	return u.withDO(u.DO.Unscoped())
}

func (u uorderDo) Create(values ...*entity.Uorder) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u uorderDo) CreateInBatches(values []*entity.Uorder, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u uorderDo) Save(values ...*entity.Uorder) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u uorderDo) First() (*entity.Uorder, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Uorder), nil
	}
}

func (u uorderDo) Take() (*entity.Uorder, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Uorder), nil
	}
}

func (u uorderDo) Last() (*entity.Uorder, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Uorder), nil
	}
}

func (u uorderDo) Find() ([]*entity.Uorder, error) {
	result, err := u.DO.Find()
	return result.([]*entity.Uorder), err
}

func (u uorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Uorder, err error) {
	buf := make([]*entity.Uorder, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u uorderDo) FindInBatches(result *[]*entity.Uorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u uorderDo) Attrs(attrs ...field.AssignExpr) *uorderDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u uorderDo) Assign(attrs ...field.AssignExpr) *uorderDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u uorderDo) Joins(fields ...field.RelationField) *uorderDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u uorderDo) Preload(fields ...field.RelationField) *uorderDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u uorderDo) FirstOrInit() (*entity.Uorder, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Uorder), nil
	}
}

func (u uorderDo) FirstOrCreate() (*entity.Uorder, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Uorder), nil
	}
}

func (u uorderDo) FindByPage(offset int, limit int) (result []*entity.Uorder, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u uorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u uorderDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u uorderDo) Delete(models ...*entity.Uorder) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *uorderDo) withDO(do gen.Dao) *uorderDo {
	u.DO = *do.(*gen.DO)
	return u
}
