// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newErcorder(db *gorm.DB, opts ...gen.DOOption) ercorder {
	_ercorder := ercorder{}

	_ercorder.ercorderDo.UseDB(db, opts...)
	_ercorder.ercorderDo.UseModel(&entity.Ercorder{})

	tableName := _ercorder.ercorderDo.TableName()
	_ercorder.ALL = field.NewAsterisk(tableName)
	_ercorder.ID = field.NewInt32(tableName, "id")
	_ercorder.Transactionid = field.NewString(tableName, "transactionid")
	_ercorder.From = field.NewString(tableName, "from")
	_ercorder.To = field.NewString(tableName, "to")
	_ercorder.Value = field.NewString(tableName, "value")
	_ercorder.BlockTimestamp = field.NewInt32(tableName, "block_timestamp")

	_ercorder.fillFieldMap()

	return _ercorder
}

type ercorder struct {
	ercorderDo

	ALL            field.Asterisk
	ID             field.Int32  // id
	Transactionid  field.String // transactionid
	From           field.String // from
	To             field.String // to
	Value          field.String // value
	BlockTimestamp field.Int32  // 时间

	fieldMap map[string]field.Expr
}

func (e ercorder) Table(newTableName string) *ercorder {
	e.ercorderDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e ercorder) As(alias string) *ercorder {
	e.ercorderDo.DO = *(e.ercorderDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *ercorder) updateTableName(table string) *ercorder {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt32(table, "id")
	e.Transactionid = field.NewString(table, "transactionid")
	e.From = field.NewString(table, "from")
	e.To = field.NewString(table, "to")
	e.Value = field.NewString(table, "value")
	e.BlockTimestamp = field.NewInt32(table, "block_timestamp")

	e.fillFieldMap()

	return e
}

func (e *ercorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *ercorder) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 6)
	e.fieldMap["id"] = e.ID
	e.fieldMap["transactionid"] = e.Transactionid
	e.fieldMap["from"] = e.From
	e.fieldMap["to"] = e.To
	e.fieldMap["value"] = e.Value
	e.fieldMap["block_timestamp"] = e.BlockTimestamp
}

func (e ercorder) clone(db *gorm.DB) ercorder {
	e.ercorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e ercorder) replaceDB(db *gorm.DB) ercorder {
	e.ercorderDo.ReplaceDB(db)
	return e
}

type ercorderDo struct{ gen.DO }

func (e ercorderDo) Debug() *ercorderDo {
	return e.withDO(e.DO.Debug())
}

func (e ercorderDo) WithContext(ctx context.Context) *ercorderDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e ercorderDo) ReadDB() *ercorderDo {
	return e.Clauses(dbresolver.Read)
}

func (e ercorderDo) WriteDB() *ercorderDo {
	return e.Clauses(dbresolver.Write)
}

func (e ercorderDo) Session(config *gorm.Session) *ercorderDo {
	return e.withDO(e.DO.Session(config))
}

func (e ercorderDo) Clauses(conds ...clause.Expression) *ercorderDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e ercorderDo) Returning(value interface{}, columns ...string) *ercorderDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e ercorderDo) Not(conds ...gen.Condition) *ercorderDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e ercorderDo) Or(conds ...gen.Condition) *ercorderDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e ercorderDo) Select(conds ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e ercorderDo) Where(conds ...gen.Condition) *ercorderDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e ercorderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *ercorderDo {
	return e.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (e ercorderDo) Order(conds ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e ercorderDo) Distinct(cols ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e ercorderDo) Omit(cols ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e ercorderDo) Join(table schema.Tabler, on ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e ercorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e ercorderDo) RightJoin(table schema.Tabler, on ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e ercorderDo) Group(cols ...field.Expr) *ercorderDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e ercorderDo) Having(conds ...gen.Condition) *ercorderDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e ercorderDo) Limit(limit int) *ercorderDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e ercorderDo) Offset(offset int) *ercorderDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e ercorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *ercorderDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e ercorderDo) Unscoped() *ercorderDo {
	return e.withDO(e.DO.Unscoped())
}

func (e ercorderDo) Create(values ...*entity.Ercorder) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e ercorderDo) CreateInBatches(values []*entity.Ercorder, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e ercorderDo) Save(values ...*entity.Ercorder) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e ercorderDo) First() (*entity.Ercorder, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Ercorder), nil
	}
}

func (e ercorderDo) Take() (*entity.Ercorder, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Ercorder), nil
	}
}

func (e ercorderDo) Last() (*entity.Ercorder, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Ercorder), nil
	}
}

func (e ercorderDo) Find() ([]*entity.Ercorder, error) {
	result, err := e.DO.Find()
	return result.([]*entity.Ercorder), err
}

func (e ercorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Ercorder, err error) {
	buf := make([]*entity.Ercorder, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e ercorderDo) FindInBatches(result *[]*entity.Ercorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e ercorderDo) Attrs(attrs ...field.AssignExpr) *ercorderDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e ercorderDo) Assign(attrs ...field.AssignExpr) *ercorderDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e ercorderDo) Joins(fields ...field.RelationField) *ercorderDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e ercorderDo) Preload(fields ...field.RelationField) *ercorderDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e ercorderDo) FirstOrInit() (*entity.Ercorder, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Ercorder), nil
	}
}

func (e ercorderDo) FirstOrCreate() (*entity.Ercorder, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Ercorder), nil
	}
}

func (e ercorderDo) FindByPage(offset int, limit int) (result []*entity.Ercorder, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e ercorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e ercorderDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e ercorderDo) Delete(models ...*entity.Ercorder) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *ercorderDo) withDO(do gen.Dao) *ercorderDo {
	e.DO = *do.(*gen.DO)
	return e
}
