// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newWhitelist(db *gorm.DB, opts ...gen.DOOption) whitelist {
	_whitelist := whitelist{}

	_whitelist.whitelistDo.UseDB(db, opts...)
	_whitelist.whitelistDo.UseModel(&entity.Whitelist{})

	tableName := _whitelist.whitelistDo.TableName()
	_whitelist.ALL = field.NewAsterisk(tableName)
	_whitelist.ID = field.NewInt64(tableName, "id")
	_whitelist.CreatedAt = field.NewTime(tableName, "created_at")
	_whitelist.UpdatedAt = field.NewTime(tableName, "updated_at")
	_whitelist.DeletedAt = field.NewField(tableName, "deleted_at")
	_whitelist.Type = field.NewInt32(tableName, "type")
	_whitelist.UserID = field.NewInt32(tableName, "user_id")
	_whitelist.Content = field.NewString(tableName, "content")
	_whitelist.Username = field.NewString(tableName, "username")

	_whitelist.fillFieldMap()

	return _whitelist
}

type whitelist struct {
	whitelistDo

	ALL       field.Asterisk
	ID        field.Int64
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Type      field.Int32  // 1:商户白名单
	UserID    field.Int32  // 商户或代理ID
	Content   field.String // IP地址
	Username  field.String // 用户名

	fieldMap map[string]field.Expr
}

func (w whitelist) Table(newTableName string) *whitelist {
	w.whitelistDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w whitelist) As(alias string) *whitelist {
	w.whitelistDo.DO = *(w.whitelistDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *whitelist) updateTableName(table string) *whitelist {
	w.ALL = field.NewAsterisk(table)
	w.ID = field.NewInt64(table, "id")
	w.CreatedAt = field.NewTime(table, "created_at")
	w.UpdatedAt = field.NewTime(table, "updated_at")
	w.DeletedAt = field.NewField(table, "deleted_at")
	w.Type = field.NewInt32(table, "type")
	w.UserID = field.NewInt32(table, "user_id")
	w.Content = field.NewString(table, "content")
	w.Username = field.NewString(table, "username")

	w.fillFieldMap()

	return w
}

func (w *whitelist) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *whitelist) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 8)
	w.fieldMap["id"] = w.ID
	w.fieldMap["created_at"] = w.CreatedAt
	w.fieldMap["updated_at"] = w.UpdatedAt
	w.fieldMap["deleted_at"] = w.DeletedAt
	w.fieldMap["type"] = w.Type
	w.fieldMap["user_id"] = w.UserID
	w.fieldMap["content"] = w.Content
	w.fieldMap["username"] = w.Username
}

func (w whitelist) clone(db *gorm.DB) whitelist {
	w.whitelistDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w whitelist) replaceDB(db *gorm.DB) whitelist {
	w.whitelistDo.ReplaceDB(db)
	return w
}

type whitelistDo struct{ gen.DO }

func (w whitelistDo) Debug() *whitelistDo {
	return w.withDO(w.DO.Debug())
}

func (w whitelistDo) WithContext(ctx context.Context) *whitelistDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w whitelistDo) ReadDB() *whitelistDo {
	return w.Clauses(dbresolver.Read)
}

func (w whitelistDo) WriteDB() *whitelistDo {
	return w.Clauses(dbresolver.Write)
}

func (w whitelistDo) Session(config *gorm.Session) *whitelistDo {
	return w.withDO(w.DO.Session(config))
}

func (w whitelistDo) Clauses(conds ...clause.Expression) *whitelistDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w whitelistDo) Returning(value interface{}, columns ...string) *whitelistDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w whitelistDo) Not(conds ...gen.Condition) *whitelistDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w whitelistDo) Or(conds ...gen.Condition) *whitelistDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w whitelistDo) Select(conds ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w whitelistDo) Where(conds ...gen.Condition) *whitelistDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w whitelistDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *whitelistDo {
	return w.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (w whitelistDo) Order(conds ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w whitelistDo) Distinct(cols ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w whitelistDo) Omit(cols ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w whitelistDo) Join(table schema.Tabler, on ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w whitelistDo) LeftJoin(table schema.Tabler, on ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w whitelistDo) RightJoin(table schema.Tabler, on ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w whitelistDo) Group(cols ...field.Expr) *whitelistDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w whitelistDo) Having(conds ...gen.Condition) *whitelistDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w whitelistDo) Limit(limit int) *whitelistDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w whitelistDo) Offset(offset int) *whitelistDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w whitelistDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *whitelistDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w whitelistDo) Unscoped() *whitelistDo {
	return w.withDO(w.DO.Unscoped())
}

func (w whitelistDo) Create(values ...*entity.Whitelist) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w whitelistDo) CreateInBatches(values []*entity.Whitelist, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w whitelistDo) Save(values ...*entity.Whitelist) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w whitelistDo) First() (*entity.Whitelist, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Whitelist), nil
	}
}

func (w whitelistDo) Take() (*entity.Whitelist, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Whitelist), nil
	}
}

func (w whitelistDo) Last() (*entity.Whitelist, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Whitelist), nil
	}
}

func (w whitelistDo) Find() ([]*entity.Whitelist, error) {
	result, err := w.DO.Find()
	return result.([]*entity.Whitelist), err
}

func (w whitelistDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Whitelist, err error) {
	buf := make([]*entity.Whitelist, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w whitelistDo) FindInBatches(result *[]*entity.Whitelist, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w whitelistDo) Attrs(attrs ...field.AssignExpr) *whitelistDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w whitelistDo) Assign(attrs ...field.AssignExpr) *whitelistDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w whitelistDo) Joins(fields ...field.RelationField) *whitelistDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w whitelistDo) Preload(fields ...field.RelationField) *whitelistDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w whitelistDo) FirstOrInit() (*entity.Whitelist, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Whitelist), nil
	}
}

func (w whitelistDo) FirstOrCreate() (*entity.Whitelist, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Whitelist), nil
	}
}

func (w whitelistDo) FindByPage(offset int, limit int) (result []*entity.Whitelist, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w whitelistDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w whitelistDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w whitelistDo) Delete(models ...*entity.Whitelist) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *whitelistDo) withDO(do gen.Dao) *whitelistDo {
	w.DO = *do.(*gen.DO)
	return w
}
