// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newBscorder(db *gorm.DB, opts ...gen.DOOption) bscorder {
	_bscorder := bscorder{}

	_bscorder.bscorderDo.UseDB(db, opts...)
	_bscorder.bscorderDo.UseModel(&entity.Bscorder{})

	tableName := _bscorder.bscorderDo.TableName()
	_bscorder.ALL = field.NewAsterisk(tableName)
	_bscorder.ID = field.NewInt32(tableName, "id")
	_bscorder.Transactionid = field.NewString(tableName, "transactionid")
	_bscorder.From = field.NewString(tableName, "from")
	_bscorder.To = field.NewString(tableName, "to")
	_bscorder.Value = field.NewString(tableName, "value")
	_bscorder.BlockTimestamp = field.NewInt32(tableName, "block_timestamp")

	_bscorder.fillFieldMap()

	return _bscorder
}

type bscorder struct {
	bscorderDo

	ALL            field.Asterisk
	ID             field.Int32  // id
	Transactionid  field.String // transactionid
	From           field.String // from
	To             field.String // to
	Value          field.String // value
	BlockTimestamp field.Int32  // 时间

	fieldMap map[string]field.Expr
}

func (b bscorder) Table(newTableName string) *bscorder {
	b.bscorderDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b bscorder) As(alias string) *bscorder {
	b.bscorderDo.DO = *(b.bscorderDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *bscorder) updateTableName(table string) *bscorder {
	b.ALL = field.NewAsterisk(table)
	b.ID = field.NewInt32(table, "id")
	b.Transactionid = field.NewString(table, "transactionid")
	b.From = field.NewString(table, "from")
	b.To = field.NewString(table, "to")
	b.Value = field.NewString(table, "value")
	b.BlockTimestamp = field.NewInt32(table, "block_timestamp")

	b.fillFieldMap()

	return b
}

func (b *bscorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *bscorder) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 6)
	b.fieldMap["id"] = b.ID
	b.fieldMap["transactionid"] = b.Transactionid
	b.fieldMap["from"] = b.From
	b.fieldMap["to"] = b.To
	b.fieldMap["value"] = b.Value
	b.fieldMap["block_timestamp"] = b.BlockTimestamp
}

func (b bscorder) clone(db *gorm.DB) bscorder {
	b.bscorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b bscorder) replaceDB(db *gorm.DB) bscorder {
	b.bscorderDo.ReplaceDB(db)
	return b
}

type bscorderDo struct{ gen.DO }

func (b bscorderDo) Debug() *bscorderDo {
	return b.withDO(b.DO.Debug())
}

func (b bscorderDo) WithContext(ctx context.Context) *bscorderDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b bscorderDo) ReadDB() *bscorderDo {
	return b.Clauses(dbresolver.Read)
}

func (b bscorderDo) WriteDB() *bscorderDo {
	return b.Clauses(dbresolver.Write)
}

func (b bscorderDo) Session(config *gorm.Session) *bscorderDo {
	return b.withDO(b.DO.Session(config))
}

func (b bscorderDo) Clauses(conds ...clause.Expression) *bscorderDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b bscorderDo) Returning(value interface{}, columns ...string) *bscorderDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b bscorderDo) Not(conds ...gen.Condition) *bscorderDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b bscorderDo) Or(conds ...gen.Condition) *bscorderDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b bscorderDo) Select(conds ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b bscorderDo) Where(conds ...gen.Condition) *bscorderDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b bscorderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *bscorderDo {
	return b.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (b bscorderDo) Order(conds ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b bscorderDo) Distinct(cols ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b bscorderDo) Omit(cols ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b bscorderDo) Join(table schema.Tabler, on ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b bscorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b bscorderDo) RightJoin(table schema.Tabler, on ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b bscorderDo) Group(cols ...field.Expr) *bscorderDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b bscorderDo) Having(conds ...gen.Condition) *bscorderDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b bscorderDo) Limit(limit int) *bscorderDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b bscorderDo) Offset(offset int) *bscorderDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b bscorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *bscorderDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b bscorderDo) Unscoped() *bscorderDo {
	return b.withDO(b.DO.Unscoped())
}

func (b bscorderDo) Create(values ...*entity.Bscorder) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b bscorderDo) CreateInBatches(values []*entity.Bscorder, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b bscorderDo) Save(values ...*entity.Bscorder) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b bscorderDo) First() (*entity.Bscorder, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Bscorder), nil
	}
}

func (b bscorderDo) Take() (*entity.Bscorder, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Bscorder), nil
	}
}

func (b bscorderDo) Last() (*entity.Bscorder, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Bscorder), nil
	}
}

func (b bscorderDo) Find() ([]*entity.Bscorder, error) {
	result, err := b.DO.Find()
	return result.([]*entity.Bscorder), err
}

func (b bscorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Bscorder, err error) {
	buf := make([]*entity.Bscorder, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b bscorderDo) FindInBatches(result *[]*entity.Bscorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b bscorderDo) Attrs(attrs ...field.AssignExpr) *bscorderDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b bscorderDo) Assign(attrs ...field.AssignExpr) *bscorderDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b bscorderDo) Joins(fields ...field.RelationField) *bscorderDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b bscorderDo) Preload(fields ...field.RelationField) *bscorderDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b bscorderDo) FirstOrInit() (*entity.Bscorder, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Bscorder), nil
	}
}

func (b bscorderDo) FirstOrCreate() (*entity.Bscorder, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Bscorder), nil
	}
}

func (b bscorderDo) FindByPage(offset int, limit int) (result []*entity.Bscorder, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b bscorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b bscorderDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b bscorderDo) Delete(models ...*entity.Bscorder) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *bscorderDo) withDO(do gen.Dao) *bscorderDo {
	b.DO = *do.(*gen.DO)
	return b
}
