package dal

import (
	"Mapi/config"
	"Mapi/model/entity"
	"log"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB *gorm.DB
)

func dbMigrate() {
	db := DB.Session(&gorm.Session{
		Logger: DB.Logger.LogMode(logger.Warn),
	})
	err := db.AutoMigrate(
		&entity.Address{},
		&entity.AdminRole{},
		&entity.Admin{},
		&entity.Adminslog{},
		&entity.Agent{},
		&entity.Cfg{},
		&entity.Ercorder{},
		&entity.Bscorder{},
		&entity.Lockaddress{},
		&entity.Merchant{},
		&entity.Merchantlog{},
		&entity.Order{},
		&entity.Payorder{},
		&entity.Permission{},
		&entity.RolePermission{},
		&entity.Role{},
		&entity.Settlement{},
		&entity.Tran{},
		&entity.AgentTran{},
		&entity.Trcorder{},
		&entity.Whitelist{},
		&entity.Lease{},
		&entity.Balance{},
	)
	if err != nil {
		log.Fatal("failed auto migrate db", zap.Error(err))
	}
}

func NewGormDB(
	conf *config.Config,
	log *zap.Logger,
) *gorm.DB {
	path := strings.Join([]string{
		conf.Db.Username, ":", conf.Db.Password,
		"@(", conf.Db.Host, ":", conf.Db.Port, ")/",
		conf.Db.Database, "?charset=utf8mb4&parseTime=true"}, "",
	)
	DB, _ = gorm.Open(mysql.Open(path), &gorm.Config{
		// Logger: logger.Default.LogMode(logger.Info),
	})
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatal("get database connection error")
	}
	sqlDB.SetMaxIdleConns(200)
	sqlDB.SetMaxOpenConns(300)
	sqlDB.SetConnMaxIdleTime(time.Hour)
	SetDefault(DB)
	dbMigrate()
	return DB
}
