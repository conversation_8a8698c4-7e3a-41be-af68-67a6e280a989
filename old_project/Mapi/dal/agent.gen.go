// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newAgent(db *gorm.DB, opts ...gen.DOOption) agent {
	_agent := agent{}

	_agent.agentDo.UseDB(db, opts...)
	_agent.agentDo.UseModel(&entity.Agent{})

	tableName := _agent.agentDo.TableName()
	_agent.ALL = field.NewAsterisk(tableName)
	_agent.ID = field.NewInt64(tableName, "id")
	_agent.CreatedAt = field.NewTime(tableName, "created_at")
	_agent.UpdatedAt = field.NewTime(tableName, "updated_at")
	_agent.DeletedAt = field.NewField(tableName, "deleted_at")
	_agent.Username = field.NewString(tableName, "username")
	_agent.Password = field.NewString(tableName, "password")
	_agent.Fee = field.NewFloat64(tableName, "fee")
	_agent.Ethfee = field.NewFloat64(tableName, "ethfee")
	_agent.Money = field.NewFloat64(tableName, "money")
	_agent.LockMoney = field.NewFloat64(tableName, "lock_money")
	_agent.ErclockMoney = field.NewFloat64(tableName, "erclock_money")
	_agent.BsclockMoney = field.NewFloat64(tableName, "bsclock_money")
	_agent.Ercmoney = field.NewFloat64(tableName, "ercmoney")
	_agent.Bscmoney = field.NewFloat64(tableName, "bscmoney")
	_agent.Bscfee = field.NewFloat64(tableName, "bscfee")
	_agent.Secret = field.NewString(tableName, "secret")

	_agent.fillFieldMap()

	return _agent
}

type agent struct {
	agentDo

	ALL          field.Asterisk
	ID           field.Int64
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Username     field.String  // 用户名
	Password     field.String  // 密码
	Fee          field.Float64 // 费率
	Ethfee       field.Float64 // eth费率
	Money        field.Float64 // 余额
	LockMoney    field.Float64 // 锁定余额
	ErclockMoney field.Float64 // ERC锁定余额
	BsclockMoney field.Float64 // BSC锁定余额
	Ercmoney     field.Float64 // ERC余额
	Bscmoney     field.Float64 // BSC余额
	Bscfee       field.Float64 // bsc费率
	Secret       field.String  // Google密钥

	fieldMap map[string]field.Expr
}

func (a agent) Table(newTableName string) *agent {
	a.agentDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a agent) As(alias string) *agent {
	a.agentDo.DO = *(a.agentDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *agent) updateTableName(table string) *agent {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")
	a.Username = field.NewString(table, "username")
	a.Password = field.NewString(table, "password")
	a.Fee = field.NewFloat64(table, "fee")
	a.Ethfee = field.NewFloat64(table, "ethfee")
	a.Money = field.NewFloat64(table, "money")
	a.LockMoney = field.NewFloat64(table, "lock_money")
	a.ErclockMoney = field.NewFloat64(table, "erclock_money")
	a.BsclockMoney = field.NewFloat64(table, "bsclock_money")
	a.Ercmoney = field.NewFloat64(table, "ercmoney")
	a.Bscmoney = field.NewFloat64(table, "bscmoney")
	a.Bscfee = field.NewFloat64(table, "bscfee")
	a.Secret = field.NewString(table, "secret")

	a.fillFieldMap()

	return a
}

func (a *agent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *agent) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 16)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["username"] = a.Username
	a.fieldMap["password"] = a.Password
	a.fieldMap["fee"] = a.Fee
	a.fieldMap["ethfee"] = a.Ethfee
	a.fieldMap["money"] = a.Money
	a.fieldMap["lock_money"] = a.LockMoney
	a.fieldMap["erclock_money"] = a.ErclockMoney
	a.fieldMap["bsclock_money"] = a.BsclockMoney
	a.fieldMap["ercmoney"] = a.Ercmoney
	a.fieldMap["bscmoney"] = a.Bscmoney
	a.fieldMap["bscfee"] = a.Bscfee
	a.fieldMap["secret"] = a.Secret
}

func (a agent) clone(db *gorm.DB) agent {
	a.agentDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a agent) replaceDB(db *gorm.DB) agent {
	a.agentDo.ReplaceDB(db)
	return a
}

type agentDo struct{ gen.DO }

func (a agentDo) Debug() *agentDo {
	return a.withDO(a.DO.Debug())
}

func (a agentDo) WithContext(ctx context.Context) *agentDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a agentDo) ReadDB() *agentDo {
	return a.Clauses(dbresolver.Read)
}

func (a agentDo) WriteDB() *agentDo {
	return a.Clauses(dbresolver.Write)
}

func (a agentDo) Session(config *gorm.Session) *agentDo {
	return a.withDO(a.DO.Session(config))
}

func (a agentDo) Clauses(conds ...clause.Expression) *agentDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a agentDo) Returning(value interface{}, columns ...string) *agentDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a agentDo) Not(conds ...gen.Condition) *agentDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a agentDo) Or(conds ...gen.Condition) *agentDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a agentDo) Select(conds ...field.Expr) *agentDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a agentDo) Where(conds ...gen.Condition) *agentDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a agentDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *agentDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a agentDo) Order(conds ...field.Expr) *agentDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a agentDo) Distinct(cols ...field.Expr) *agentDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a agentDo) Omit(cols ...field.Expr) *agentDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a agentDo) Join(table schema.Tabler, on ...field.Expr) *agentDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a agentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *agentDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a agentDo) RightJoin(table schema.Tabler, on ...field.Expr) *agentDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a agentDo) Group(cols ...field.Expr) *agentDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a agentDo) Having(conds ...gen.Condition) *agentDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a agentDo) Limit(limit int) *agentDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a agentDo) Offset(offset int) *agentDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a agentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *agentDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a agentDo) Unscoped() *agentDo {
	return a.withDO(a.DO.Unscoped())
}

func (a agentDo) Create(values ...*entity.Agent) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a agentDo) CreateInBatches(values []*entity.Agent, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a agentDo) Save(values ...*entity.Agent) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a agentDo) First() (*entity.Agent, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Agent), nil
	}
}

func (a agentDo) Take() (*entity.Agent, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Agent), nil
	}
}

func (a agentDo) Last() (*entity.Agent, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Agent), nil
	}
}

func (a agentDo) Find() ([]*entity.Agent, error) {
	result, err := a.DO.Find()
	return result.([]*entity.Agent), err
}

func (a agentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Agent, err error) {
	buf := make([]*entity.Agent, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a agentDo) FindInBatches(result *[]*entity.Agent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a agentDo) Attrs(attrs ...field.AssignExpr) *agentDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a agentDo) Assign(attrs ...field.AssignExpr) *agentDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a agentDo) Joins(fields ...field.RelationField) *agentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a agentDo) Preload(fields ...field.RelationField) *agentDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a agentDo) FirstOrInit() (*entity.Agent, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Agent), nil
	}
}

func (a agentDo) FirstOrCreate() (*entity.Agent, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Agent), nil
	}
}

func (a agentDo) FindByPage(offset int, limit int) (result []*entity.Agent, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a agentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a agentDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a agentDo) Delete(models ...*entity.Agent) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *agentDo) withDO(do gen.Dao) *agentDo {
	a.DO = *do.(*gen.DO)
	return a
}
