// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newTrcorder(db *gorm.DB, opts ...gen.DOOption) trcorder {
	_trcorder := trcorder{}

	_trcorder.trcorderDo.UseDB(db, opts...)
	_trcorder.trcorderDo.UseModel(&entity.Trcorder{})

	tableName := _trcorder.trcorderDo.TableName()
	_trcorder.ALL = field.NewAsterisk(tableName)
	_trcorder.ID = field.NewInt32(tableName, "id")
	_trcorder.Transactionid = field.NewString(tableName, "transactionid")
	_trcorder.From = field.NewString(tableName, "from")
	_trcorder.To = field.NewString(tableName, "to")
	_trcorder.Value = field.NewString(tableName, "value")
	_trcorder.BlockTimestamp = field.NewInt32(tableName, "block_timestamp")

	_trcorder.fillFieldMap()

	return _trcorder
}

type trcorder struct {
	trcorderDo

	ALL            field.Asterisk
	ID             field.Int32  // id
	Transactionid  field.String // transactionid
	From           field.String // from
	To             field.String // to
	Value          field.String // value
	BlockTimestamp field.Int32  // block_timestamp

	fieldMap map[string]field.Expr
}

func (t trcorder) Table(newTableName string) *trcorder {
	t.trcorderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t trcorder) As(alias string) *trcorder {
	t.trcorderDo.DO = *(t.trcorderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *trcorder) updateTableName(table string) *trcorder {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.Transactionid = field.NewString(table, "transactionid")
	t.From = field.NewString(table, "from")
	t.To = field.NewString(table, "to")
	t.Value = field.NewString(table, "value")
	t.BlockTimestamp = field.NewInt32(table, "block_timestamp")

	t.fillFieldMap()

	return t
}

func (t *trcorder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *trcorder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 6)
	t.fieldMap["id"] = t.ID
	t.fieldMap["transactionid"] = t.Transactionid
	t.fieldMap["from"] = t.From
	t.fieldMap["to"] = t.To
	t.fieldMap["value"] = t.Value
	t.fieldMap["block_timestamp"] = t.BlockTimestamp
}

func (t trcorder) clone(db *gorm.DB) trcorder {
	t.trcorderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t trcorder) replaceDB(db *gorm.DB) trcorder {
	t.trcorderDo.ReplaceDB(db)
	return t
}

type trcorderDo struct{ gen.DO }

func (t trcorderDo) Debug() *trcorderDo {
	return t.withDO(t.DO.Debug())
}

func (t trcorderDo) WithContext(ctx context.Context) *trcorderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t trcorderDo) ReadDB() *trcorderDo {
	return t.Clauses(dbresolver.Read)
}

func (t trcorderDo) WriteDB() *trcorderDo {
	return t.Clauses(dbresolver.Write)
}

func (t trcorderDo) Session(config *gorm.Session) *trcorderDo {
	return t.withDO(t.DO.Session(config))
}

func (t trcorderDo) Clauses(conds ...clause.Expression) *trcorderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t trcorderDo) Returning(value interface{}, columns ...string) *trcorderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t trcorderDo) Not(conds ...gen.Condition) *trcorderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t trcorderDo) Or(conds ...gen.Condition) *trcorderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t trcorderDo) Select(conds ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t trcorderDo) Where(conds ...gen.Condition) *trcorderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t trcorderDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *trcorderDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t trcorderDo) Order(conds ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t trcorderDo) Distinct(cols ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t trcorderDo) Omit(cols ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t trcorderDo) Join(table schema.Tabler, on ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t trcorderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t trcorderDo) RightJoin(table schema.Tabler, on ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t trcorderDo) Group(cols ...field.Expr) *trcorderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t trcorderDo) Having(conds ...gen.Condition) *trcorderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t trcorderDo) Limit(limit int) *trcorderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t trcorderDo) Offset(offset int) *trcorderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t trcorderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *trcorderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t trcorderDo) Unscoped() *trcorderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t trcorderDo) Create(values ...*entity.Trcorder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t trcorderDo) CreateInBatches(values []*entity.Trcorder, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t trcorderDo) Save(values ...*entity.Trcorder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t trcorderDo) First() (*entity.Trcorder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Trcorder), nil
	}
}

func (t trcorderDo) Take() (*entity.Trcorder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Trcorder), nil
	}
}

func (t trcorderDo) Last() (*entity.Trcorder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Trcorder), nil
	}
}

func (t trcorderDo) Find() ([]*entity.Trcorder, error) {
	result, err := t.DO.Find()
	return result.([]*entity.Trcorder), err
}

func (t trcorderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Trcorder, err error) {
	buf := make([]*entity.Trcorder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t trcorderDo) FindInBatches(result *[]*entity.Trcorder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t trcorderDo) Attrs(attrs ...field.AssignExpr) *trcorderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t trcorderDo) Assign(attrs ...field.AssignExpr) *trcorderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t trcorderDo) Joins(fields ...field.RelationField) *trcorderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t trcorderDo) Preload(fields ...field.RelationField) *trcorderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t trcorderDo) FirstOrInit() (*entity.Trcorder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Trcorder), nil
	}
}

func (t trcorderDo) FirstOrCreate() (*entity.Trcorder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Trcorder), nil
	}
}

func (t trcorderDo) FindByPage(offset int, limit int) (result []*entity.Trcorder, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t trcorderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t trcorderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t trcorderDo) Delete(models ...*entity.Trcorder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *trcorderDo) withDO(do gen.Dao) *trcorderDo {
	t.DO = *do.(*gen.DO)
	return t
}
