// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"Mapi/model/entity"
)

func newAdminRole(db *gorm.DB, opts ...gen.DOOption) adminRole {
	_adminRole := adminRole{}

	_adminRole.adminRoleDo.UseDB(db, opts...)
	_adminRole.adminRoleDo.UseModel(&entity.AdminRole{})

	tableName := _adminRole.adminRoleDo.TableName()
	_adminRole.ALL = field.NewAsterisk(tableName)
	_adminRole.ID = field.NewInt32(tableName, "id")
	_adminRole.Adminid = field.NewInt32(tableName, "adminid")
	_adminRole.RoleID = field.NewInt32(tableName, "role_id")
	_adminRole.Ctime = field.NewInt32(tableName, "ctime")
	_adminRole.DelFlg = field.NewInt32(tableName, "del_flg")

	_adminRole.fillFieldMap()

	return _adminRole
}

type adminRole struct {
	adminRoleDo

	ALL     field.Asterisk
	ID      field.Int32
	Adminid field.Int32 // admin表的id
	RoleID  field.Int32 // roles表的id
	Ctime   field.Int32
	DelFlg  field.Int32

	fieldMap map[string]field.Expr
}

func (a adminRole) Table(newTableName string) *adminRole {
	a.adminRoleDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a adminRole) As(alias string) *adminRole {
	a.adminRoleDo.DO = *(a.adminRoleDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *adminRole) updateTableName(table string) *adminRole {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt32(table, "id")
	a.Adminid = field.NewInt32(table, "adminid")
	a.RoleID = field.NewInt32(table, "role_id")
	a.Ctime = field.NewInt32(table, "ctime")
	a.DelFlg = field.NewInt32(table, "del_flg")

	a.fillFieldMap()

	return a
}

func (a *adminRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *adminRole) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 5)
	a.fieldMap["id"] = a.ID
	a.fieldMap["adminid"] = a.Adminid
	a.fieldMap["role_id"] = a.RoleID
	a.fieldMap["ctime"] = a.Ctime
	a.fieldMap["del_flg"] = a.DelFlg
}

func (a adminRole) clone(db *gorm.DB) adminRole {
	a.adminRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a adminRole) replaceDB(db *gorm.DB) adminRole {
	a.adminRoleDo.ReplaceDB(db)
	return a
}

type adminRoleDo struct{ gen.DO }

func (a adminRoleDo) Debug() *adminRoleDo {
	return a.withDO(a.DO.Debug())
}

func (a adminRoleDo) WithContext(ctx context.Context) *adminRoleDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a adminRoleDo) ReadDB() *adminRoleDo {
	return a.Clauses(dbresolver.Read)
}

func (a adminRoleDo) WriteDB() *adminRoleDo {
	return a.Clauses(dbresolver.Write)
}

func (a adminRoleDo) Session(config *gorm.Session) *adminRoleDo {
	return a.withDO(a.DO.Session(config))
}

func (a adminRoleDo) Clauses(conds ...clause.Expression) *adminRoleDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a adminRoleDo) Returning(value interface{}, columns ...string) *adminRoleDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a adminRoleDo) Not(conds ...gen.Condition) *adminRoleDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a adminRoleDo) Or(conds ...gen.Condition) *adminRoleDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a adminRoleDo) Select(conds ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a adminRoleDo) Where(conds ...gen.Condition) *adminRoleDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a adminRoleDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *adminRoleDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a adminRoleDo) Order(conds ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a adminRoleDo) Distinct(cols ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a adminRoleDo) Omit(cols ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a adminRoleDo) Join(table schema.Tabler, on ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a adminRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a adminRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a adminRoleDo) Group(cols ...field.Expr) *adminRoleDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a adminRoleDo) Having(conds ...gen.Condition) *adminRoleDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a adminRoleDo) Limit(limit int) *adminRoleDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a adminRoleDo) Offset(offset int) *adminRoleDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a adminRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *adminRoleDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a adminRoleDo) Unscoped() *adminRoleDo {
	return a.withDO(a.DO.Unscoped())
}

func (a adminRoleDo) Create(values ...*entity.AdminRole) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a adminRoleDo) CreateInBatches(values []*entity.AdminRole, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a adminRoleDo) Save(values ...*entity.AdminRole) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a adminRoleDo) First() (*entity.AdminRole, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AdminRole), nil
	}
}

func (a adminRoleDo) Take() (*entity.AdminRole, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AdminRole), nil
	}
}

func (a adminRoleDo) Last() (*entity.AdminRole, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AdminRole), nil
	}
}

func (a adminRoleDo) Find() ([]*entity.AdminRole, error) {
	result, err := a.DO.Find()
	return result.([]*entity.AdminRole), err
}

func (a adminRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.AdminRole, err error) {
	buf := make([]*entity.AdminRole, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a adminRoleDo) FindInBatches(result *[]*entity.AdminRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a adminRoleDo) Attrs(attrs ...field.AssignExpr) *adminRoleDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a adminRoleDo) Assign(attrs ...field.AssignExpr) *adminRoleDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a adminRoleDo) Joins(fields ...field.RelationField) *adminRoleDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a adminRoleDo) Preload(fields ...field.RelationField) *adminRoleDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a adminRoleDo) FirstOrInit() (*entity.AdminRole, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AdminRole), nil
	}
}

func (a adminRoleDo) FirstOrCreate() (*entity.AdminRole, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.AdminRole), nil
	}
}

func (a adminRoleDo) FindByPage(offset int, limit int) (result []*entity.AdminRole, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a adminRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a adminRoleDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a adminRoleDo) Delete(models ...*entity.AdminRole) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *adminRoleDo) withDO(do gen.Dao) *adminRoleDo {
	a.DO = *do.(*gen.DO)
	return a
}
