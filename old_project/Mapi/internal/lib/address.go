package lib

import (
	"Mapi/dal"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/crypto/chacha20poly1305"
	"golang.org/x/crypto/pbkdf2"
	"io"
	"log"
	"math/big"
	"strings"
)

func GetAddr(index uint64) (string, string, string) {
	cfgkey, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("Key")).First()
	//cfgtrc, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("TRC")).First()
	//cfgerc, _ := dal.Cfg.Where(dal.Cfg.Func.Eq("ERC")).First()
	mainPrivateKeyHex := cfgkey.Value
	privateKey, err := crypto.HexToECDSA(mainPrivateKeyHex)
	if err != nil {
		log.Fatal(err)
	}
	derivedKey := deriveKey(privateKey, index)
	publicKey := derivedKey.PublicKey
	ethAddress := crypto.PubkeyToAddress(publicKey)
	tronAddress, err := generateTronAddress(publicKey)
	if err != nil {
		log.Fatal(err)
	}
	reverseEthAddress := reverseString(ethAddress.String())
	reverseTronAddress := reverseString(tronAddress)
	salt := []byte(reverseTronAddress)
	key := pbkdf2.Key([]byte(reverseEthAddress), salt, 4096, 32, sha256.New)
	plainText := []byte(hex.EncodeToString(derivedKey.D.Bytes()))
	cipherText, err := encrypt(plainText, key)
	if err != nil {
		log.Fatal(err)
	}
	return tronAddress, ethAddress.Hex(), hex.EncodeToString(cipherText)
}

func reverseString(s string) string {
	r := []rune(s)
	for i, j := 0, len(r)-1; i < j; i, j = i+1, j-1 {
		r[i], r[j] = r[j], r[i]
	}
	return string(r)
}

func generateTronAddress(publicKey ecdsa.PublicKey) (string, error) {
	pubBytes := crypto.FromECDSAPub(&publicKey)
	hash := crypto.Keccak256(pubBytes[1:])[12:]
	address := append([]byte{0x41}, hash...)
	checksum := sha256.Sum256(address)
	checksum = sha256.Sum256(checksum[:])
	address = append(address, checksum[:4]...)
	tronAddress := base58Encode(address)
	if len(tronAddress) == 0 {
		return "", fmt.Errorf("base58Encode failed")
	}
	return tronAddress, nil
}

func base58Encode(input []byte) string {
	alphabet := []byte("**********************************************************")
	var result []byte

	x := big.NewInt(0).SetBytes(input)

	base := big.NewInt(58)
	zero := big.NewInt(0)
	mod := &big.Int{}

	for x.Cmp(zero) != 0 {
		x.DivMod(x, base, mod)
		result = append(result, alphabet[mod.Int64()])
	}

	reverseBytes(result)
	return string(result)
}

func reverseBytes(data []byte) {
	for i, j := 0, len(data)-1; i < j; i, j = i+1, j-1 {
		data[i], data[j] = data[j], data[i]
	}
}

func deriveKey(privateKey *ecdsa.PrivateKey, index uint64) *ecdsa.PrivateKey {
	curve := privateKey.Curve
	params := curve.Params()
	bigIndex := new(big.Int).SetUint64(index)
	orderBytes := params.N.Bytes()
	indexBytes := make([]byte, 32)
	copy(indexBytes[32-len(bigIndex.Bytes()):], bigIndex.Bytes())

	fmt.Printf("Big Index: %s\n", bigIndex.String())
	fmt.Printf("Index Bytes: %x\n", indexBytes)
	data := append(privateKey.D.Bytes(), orderBytes...)
	data = append(data, indexBytes...)
	hash := crypto.Keccak256(data)
	derivedKey := new(ecdsa.PrivateKey)
	derivedKey.PublicKey.Curve = curve
	derivedKey.D = new(big.Int).SetBytes(hash)
	derivedKey.PublicKey.X, derivedKey.PublicKey.Y = curve.ScalarBaseMult(hash)
	return derivedKey
}

func encrypt(plainText, key []byte) ([]byte, error) {
	aead, err := chacha20poly1305.NewX(key)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, chacha20poly1305.NonceSizeX)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	cipherText := aead.Seal(nil, nonce, plainText, nil)
	cipherText = append(nonce, cipherText...)
	return cipherText, nil
}

func decrypt(cipherText, key []byte) ([]byte, error) {
	aead, err := chacha20poly1305.NewX(key)
	if err != nil {
		return nil, err
	}

	if len(cipherText) < chacha20poly1305.NonceSizeX {
		return nil, fmt.Errorf("cipherText too short")
	}

	nonce, cipherText := cipherText[:chacha20poly1305.NonceSizeX], cipherText[chacha20poly1305.NonceSizeX:]

	plainText, err := aead.Open(nil, nonce, cipherText, nil)
	if err != nil {
		return nil, err
	}

	return plainText, nil
}

func Dok(tronAddress string, ethAddress string, encryptedKey string) string {
	salt := []byte(reverseString(tronAddress))
	key := pbkdf2.Key([]byte(reverseString(ethAddress)), salt, 4096, 32, sha256.New)

	cipherText, err := hex.DecodeString(encryptedKey)
	if err != nil {
		log.Fatal(err)
	}
	plainText, err := decrypt(cipherText, key)
	if err != nil {
		log.Fatal(err)
	}

	privateKey := string(plainText)

	// Check if the length of the decrypted private key is less than 64
	// If it is, pad it with zeros at the beginning
	if len(privateKey) < 64 {
		privateKey = strings.Repeat("0", 64-len(privateKey)) + privateKey
	}

	return privateKey
}
