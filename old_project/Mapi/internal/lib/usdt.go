package lib

import (
	"Mapi/dal"
	"context"
	"encoding/json"
	"go.uber.org/fx"
	"log"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Response 定义响应的数据结构
type Response struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstId    string `json:"instId"`
		MarkPx    string `json:"markPx"`
		Timestamp string `json:"ts"`
	} `json:"data"`
}

// CoinData 定义币种数据的结构
type CoinData struct {
	Price     string
	Timestamp string
}

func UsdtWsServer(lc fx.Lifecycle) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				UpdateUsdt() // 在新的goroutine中启动连接
			}()
			return nil
		},
	})

}

func UpdateUsdt() {
	// 定义websocket的url
	u := url.URL{Scheme: "wss", Host: "wspri.okx.com:8443", Path: "/ws/v5/ipublic"}

	// 连接websocket
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	// 定义订阅消息
	subscribeMsg := `{"op": "subscribe", "args": [{"channel":"mark-price","instId":"ETH-USDT"},{"channel":"mark-price","instId":"TRX-USDT"},{"channel":"mark-price","instId":"BNB-USDT"}]}`
	// 发送订阅消息
	err = c.WriteMessage(websocket.TextMessage, []byte(subscribeMsg))
	if err != nil {
		log.Fatal("subscribe:", err)
	}

	// 定义一个map来存储币种数据
	coinDataMap := make(map[string]CoinData)
	// 定义一个互斥锁
	mtx := &sync.Mutex{}

	// 开启一个goroutine来定时打印和清空币种数据
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		for {
			select {
			case <-ticker.C:
				mtx.Lock()
				for coin, data := range coinDataMap {
					switch coin {
					case "ETH-USDT":
						dal.Cfg.Where(dal.Cfg.Func.Eq("ETH=USDT")).Update(dal.Cfg.Value, data.Price)
					case "TRX-USDT":
						dal.Cfg.Where(dal.Cfg.Func.Eq("TRX=USDT")).Update(dal.Cfg.Value, data.Price)
					case "BNB-USDT":
						dal.Cfg.Where(dal.Cfg.Func.Eq("BNB=USDT")).Update(dal.Cfg.Value, data.Price)
					}
				}
				coinDataMap = make(map[string]CoinData) // 清空map，准备下一轮数据
				mtx.Unlock()
			}
		}
	}()

	// 主循环，读取并处理websocket消息
	for {
		_, message, err := c.ReadMessage()
		if err != nil {
			log.Println("read:", err)
			return
		}

		// 解析消息
		var resp Response
		err = json.Unmarshal(message, &resp)
		if err != nil {
			log.Println("json unmarshal:", err)
			return
		}

		// 更新币种数据
		for _, data := range resp.Data {
			mtx.Lock()
			coinDataMap[data.InstId] = CoinData{Price: data.MarkPx, Timestamp: data.Timestamp}
			mtx.Unlock()
		}
	}

}
