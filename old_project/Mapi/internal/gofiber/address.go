package gofiber

import (
	"Mapi/dal"
	"Mapi/internal/lib"
	"Mapi/model/entity"
	"fmt"
	"github.com/gofiber/fiber/v2"
	"github.com/spf13/cast"
)

func getExchangeRate(c *fiber.Ctx) error {
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  lib.GetExchange(),
	})
}

func bindAddress(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	userid := c.Query("userid")
	callbackurl := c.Query("callbackurl")
	sign := c.Query("sign")
	//校验参数
	if memberid == "" || userid == "" || sign == "" || callbackurl == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	//验签
	appkey := mch.Appkey
	signs := lib.Md5(memberid + userid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}

	check, _ := dal.Address.Where(dal.Address.Userid.Eq(userid)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.DelFlg.Eq(0)).First()
	if check != nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "此用户已绑定地址",
		})
	}

	//生成地址
	trc, erc, key, index := lib.CreateAddr()
	ling := lib.StringToFloat64("0")
	err := dal.Address.Create(&entity.Address{
		ID:           index,
		Trxaddr:      &trc,
		Ethaddr:      &erc,
		Key:          &key,
		Trx:          &ling,
		Eth:          &ling,
		UsdtErc:      &ling,
		UsdtTrc:      &ling,
		Userid:       &userid,
		Callbackurl:  &callbackurl,
		MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
		MerchantName: mch.Username,
		Num:          lib.Int64ToInt32Ptr(0),
		Lock:         lib.Int64ToInt32Ptr(0),
		DelFlg:       lib.Int64ToInt32Ptr(0),
		TrxStatus:    lib.Int64ToInt32Ptr(0),
		UpdateMoney:  lib.Int64ToInt32Ptr(0),
		Ethlock:      lib.Int64ToInt32Ptr(0),
	})
	if err != nil {
		fmt.Println(err)
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "创建地址失败",
		})
	}
	// 注释掉 WebSocket 消息发送
	// wsdatatrc := make(map[string]string)
	// wsdatatrc["type"] = "addtrc"
	// wsdatatrc["address"] = trc
	// jsonDataTrc, _ := json.Marshal(wsdatatrc)
	// lib.SendMessage(string(jsonDataTrc))
	// wsdataerc := make(map[string]string)
	// wsdataerc["type"] = "adderc"
	// wsdataerc["address"] = erc
	// jsonDataErc, _ := json.Marshal(wsdataerc)
	// lib.SendMessage(string(jsonDataErc))
	data := make(map[string]string)
	data["trc"] = trc
	data["erc"] = erc
	data["callbackurl"] = callbackurl
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": data,
	})
}

func updateAddress(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	userid := c.Query("userid")
	sign := c.Query("sign")
	//校验参数
	if memberid == "" || userid == "" || sign == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	//验签
	appkey := mch.Appkey
	signs := lib.Md5(memberid + userid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}

	check, _ := dal.Address.Where(dal.Address.Userid.Eq(userid)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.DelFlg.Eq(0)).First()
	if check == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "此用户未绑定地址",
		})
	}

	dal.Address.Where(dal.Address.ID.Eq(check.ID)).Update(dal.Address.DelFlg, 1)

	//生成地址
	trc, erc, key, index := lib.CreateAddr()
	err := dal.Address.Create(&entity.Address{
		ID:           index,
		Trxaddr:      &trc,
		Ethaddr:      &erc,
		Key:          &key,
		Userid:       &userid,
		Callbackurl:  check.Callbackurl,
		MerchantID:   lib.Int64ToInt32Ptr(mch.ID),
		MerchantName: mch.Username,
		Num:          lib.Int64ToInt32Ptr(0),
		Lock:         lib.Int64ToInt32Ptr(0),
		DelFlg:       lib.Int64ToInt32Ptr(0),
	})
	if err != nil {
		fmt.Println(err)
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "创建地址失败",
		})
	}

	data := make(map[string]string)
	data["trc"] = trc
	data["erc"] = erc
	data["callbackurl"] = *check.Callbackurl
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": data,
	})
}

func updateCallbackurl(c *fiber.Ctx) error {
	memberid := c.Query("memberid")
	userid := c.Query("userid")
	callbackurl := c.Query("callbackurl")
	sign := c.Query("sign")
	//校验参数
	if memberid == "" || userid == "" || sign == "" || callbackurl == "" {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "参数错误",
		})
	}
	mch, _ := dal.Merchant.Where(dal.Merchant.ID.Eq(cast.ToInt64(memberid))).First()
	if mch == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "商户不存在",
		})
	}
	//验签
	appkey := mch.Appkey
	signs := lib.Md5(memberid + userid + *appkey)
	if signs != sign {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "验签失败",
		})
	}

	check, _ := dal.Address.Where(dal.Address.Userid.Eq(userid)).Where(dal.Address.MerchantID.Eq(int32(mch.ID))).Where(dal.Address.DelFlg.Eq(0)).First()
	if check == nil {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"code": false,
			"msg":  "此用户未绑定地址",
		})
	}

	dal.Address.Where(dal.Address.ID.Eq(check.ID)).Update(dal.Address.Callbackurl, callbackurl)

	data := make(map[string]string)
	data["trc"] = *check.Trxaddr
	data["erc"] = *check.Ethaddr
	data["callbackurl"] = callbackurl
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"code": true,
		"msg":  "成功",
		"data": data,
	})
}
