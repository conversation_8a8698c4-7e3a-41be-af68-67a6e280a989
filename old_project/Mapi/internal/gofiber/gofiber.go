package gofiber

import (
	"context"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/fx"
	"go.uber.org/zap"
)

func NewFiberApp(logger *zap.Logger) *fiber.App {
	app := fiber.New()

	app.Use(CustomZapLogger(logger))

	// 在这里注册路由
	SetupRoutes(app)

	return app
}

func RunFiberServer(app *fiber.App, lc fx.Lifecycle) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			go func() {
				app.Listen(":3888")
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			return app.Shutdown()
		},
	})
}

func CustomZapLogger(logger *zap.Logger) fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()
		err := c.Next()
		stop := time.Now()

		logger.Info("Request",
			zap.String("method", c.Method()),
			zap.String("path", c.Path()),
			zap.String("ip", c.IP()),
			zap.String("user_agent", c.Get("User-Agent")),
			zap.Duration("duration", stop.Sub(start)),
			zap.Int("status", c.Response().StatusCode()),
		)

		return err
	}
}
